body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-style: italic;
}

.chat-form {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

#queryInput {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

#submitButton {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  min-width: 120px;
}

#submitButton:hover {
  background-color: #0056b3;
}

#submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.results {
  margin-top: 20px;
}



.welcome-message {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 30px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
}

h3 {
  color: #333;
  margin-bottom: 20px;
}

h4 {
  color: #495057;
  margin-bottom: 15px;
  margin-top: 25px;
}

/* Chat Interface Styles */
.chat-response {
  margin-top: 20px;
}

.answer-section {
  background-color: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.answer-content {
  line-height: 1.7;
  color: #333;
  font-size: 16px;
  white-space: pre-wrap;
}
