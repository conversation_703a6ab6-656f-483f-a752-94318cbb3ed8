body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

.search-form {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

#queryInput {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

#searchButton {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}

#searchButton:hover {
  background-color: #0056b3;
}

#searchButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.results {
  margin-top: 20px;
}

.result-item {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 15px;
}

.result-header {
  font-weight: bold;
  color: #495057;
  margin-bottom: 8px;
}

.result-content {
  line-height: 1.5;
  color: #333;
}

.similarity-score {
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

.no-results {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
}

h3 {
  color: #333;
  margin-bottom: 20px;
}
