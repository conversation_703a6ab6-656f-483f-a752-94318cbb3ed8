body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 10px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-style: italic;
}

.mode-toggle {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.mode-btn {
  padding: 10px 20px;
  border: 2px solid #ddd;
  background-color: white;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.mode-btn:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.mode-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.search-form {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

#queryInput {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

#submitButton {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  min-width: 120px;
}

#submitButton:hover {
  background-color: #0056b3;
}

#submitButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.results {
  margin-top: 20px;
}

.result-item {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 15px;
}

.result-header {
  font-weight: bold;
  color: #495057;
  margin-bottom: 8px;
}

.result-content {
  line-height: 1.5;
  color: #333;
}

.similarity-score {
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

.no-results {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
}

h3 {
  color: #333;
  margin-bottom: 20px;
}

h4 {
  color: #495057;
  margin-bottom: 15px;
  margin-top: 25px;
}

/* Chat Interface Styles */
.chat-response {
  margin-top: 20px;
}

.answer-section {
  background-color: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.answer-content {
  line-height: 1.6;
  color: #333;
  font-size: 16px;
  white-space: pre-wrap;
}

.confidence-score {
  font-size: 12px;
  color: #0056b3;
  margin-top: 10px;
  font-weight: bold;
}

.sources-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
}

.source-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 12px;
  margin-bottom: 10px;
}

.source-header {
  font-weight: bold;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

.source-content {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

/* Search Results Styles */
.search-results {
  margin-top: 20px;
}
