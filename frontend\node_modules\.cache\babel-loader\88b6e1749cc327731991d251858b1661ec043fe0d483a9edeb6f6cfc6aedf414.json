{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AIAgentChatbot() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: \"bot\",\n    content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n    timestamp: new Date()\n  }]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n    testBackendConnection();\n  }, []);\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim()) return;\n\n    // Add user message to chat\n    const userMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content: query,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setLoading(true);\n    setError(\"\");\n    const currentQuery = query;\n    setQuery(\"\"); // Clear input immediately\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query: currentQuery\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Add bot response to chat\n        const botMessage = {\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: data.answer,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, botMessage]);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n    setLoading(false);\n  }\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const renderMessages = () => {\n    return messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.type}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-text\",\n          children: message.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-time\",\n          children: formatTime(message.timestamp)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, message.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83E\\uDD16 AI Agent Chatbot\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"subtitle\",\n        children: \"Technical Documentation Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      children: [renderMessages(), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message bot typing\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"typing-indicator\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-text\",\n            children: \"AI is thinking...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-input-form\",\n      onSubmit: handleSubmit,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: query,\n          onChange: e => setQuery(e.target.value),\n          placeholder: \"Type your question here...\",\n          disabled: loading,\n          autoFocus: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: loading || !query.trim(),\n          children: loading ? \"⏳\" : \"📤\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n}\n_s(AIAgentChatbot, \"XI0Gl11PzOvUhnDyfnhnkCRfenw=\");\n_c = AIAgentChatbot;\nvar _c;\n$RefreshReg$(_c, \"AIAgentChatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AIAgentChatbot", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "loading", "setLoading", "error", "setError", "testBackendConnection", "response", "fetch", "data", "json", "console", "log", "handleSubmit", "e", "preventDefault", "trim", "userMessage", "now", "prev", "<PERSON><PERSON><PERSON><PERSON>", "method", "headers", "body", "JSON", "stringify", "ok", "botMessage", "answer", "message", "formatTime", "toLocaleTimeString", "hour", "minute", "renderMessages", "map", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "value", "onChange", "target", "placeholder", "disabled", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport './App.css';\n\nexport default function AIAgentChatbot() {\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: \"bot\",\n      content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n      timestamp: new Date()\n    }\n  ]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n\n    testBackendConnection();\n  }, []);\n\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim()) return;\n\n    // Add user message to chat\n    const userMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content: query,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setLoading(true);\n    setError(\"\");\n\n    const currentQuery = query;\n    setQuery(\"\"); // Clear input immediately\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ query: currentQuery }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Add bot response to chat\n        const botMessage = {\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: data.answer,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, botMessage]);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n\n    setLoading(false);\n  }\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const renderMessages = () => {\n    return messages.map((message) => (\n      <div key={message.id} className={`message ${message.type}`}>\n        <div className=\"message-content\">\n          <div className=\"message-text\">\n            {message.content}\n          </div>\n          <div className=\"message-time\">\n            {formatTime(message.timestamp)}\n          </div>\n        </div>\n      </div>\n    ));\n  };\n\n  return (\n    <div className=\"chat-container\">\n      <div className=\"chat-header\">\n        <h1>🤖 AI Agent Chatbot</h1>\n        <p className=\"subtitle\">Technical Documentation Assistant</p>\n      </div>\n\n      <div className=\"chat-messages\">\n        {renderMessages()}\n\n        {loading && (\n          <div className=\"message bot typing\">\n            <div className=\"message-content\">\n              <div className=\"typing-indicator\">\n                <span></span>\n                <span></span>\n                <span></span>\n              </div>\n              <div className=\"message-text\">AI is thinking...</div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <div className=\"error-message\">\n          {error}\n        </div>\n      )}\n\n      <form className=\"chat-input-form\" onSubmit={handleSubmit}>\n        <div className=\"input-container\">\n          <input\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            placeholder=\"Type your question here...\"\n            disabled={loading}\n            autoFocus\n          />\n          <button\n            type=\"submit\"\n            disabled={loading || !query.trim()}\n          >\n            {loading ? \"⏳\" : \"📤\"}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,CACvC;IACEU,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,kFAAkF;IAC3FC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,QAAQ,CAAC,yEAAyE,CAAC;MACrF;IACF,CAAC;IAEDC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeO,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACtB,KAAK,CAACuB,IAAI,CAAC,CAAC,EAAE;;IAEnB;IACA,MAAMC,WAAW,GAAG;MAClBpB,EAAE,EAAEI,IAAI,CAACiB,GAAG,CAAC,CAAC;MACdpB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEN,KAAK;MACdO,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3Cd,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMe,YAAY,GAAG3B,KAAK;IAC1BC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9Da,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEhC,KAAK,EAAE2B;QAAa,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMX,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACmB,EAAE,EAAE;QACf;QACA,MAAMC,UAAU,GAAG;UACjB9B,EAAE,EAAEI,IAAI,CAACiB,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBpB,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEU,IAAI,CAACmB,MAAM;UACpB5B,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDL,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEQ,UAAU,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLtB,QAAQ,CAACI,IAAI,CAACL,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,QAAQ,CAAC,iBAAiB,GAAGD,KAAK,CAACyB,OAAO,CAAC;IAC7C;IAEA1B,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAM2B,UAAU,GAAI9B,SAAS,IAAK;IAChC,OAAOA,SAAS,CAAC+B,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOvC,QAAQ,CAACwC,GAAG,CAAEN,OAAO,iBAC1BvC,OAAA;MAAsB8C,SAAS,EAAE,WAAWP,OAAO,CAAC/B,IAAI,EAAG;MAAAuC,QAAA,eACzD/C,OAAA;QAAK8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/C,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BR,OAAO,CAAC9B;QAAO;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BP,UAAU,CAACD,OAAO,CAAC7B,SAAS;QAAC;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAREZ,OAAO,CAAChC,EAAE;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASf,CACN,CAAC;EACJ,CAAC;EAED,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B/C,OAAA;MAAK8C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/C,OAAA;QAAA+C,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnD,OAAA;QAAG8C,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAENnD,OAAA;MAAK8C,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3BH,cAAc,CAAC,CAAC,EAEhBhC,OAAO,iBACNZ,OAAA;QAAK8C,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC/C,OAAA;UAAK8C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/C,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/C,OAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnD,OAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnD,OAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELrC,KAAK,iBACJd,OAAA;MAAK8C,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BjC;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnD,OAAA;MAAM8C,SAAS,EAAC,iBAAiB;MAACM,QAAQ,EAAE7B,YAAa;MAAAwB,QAAA,eACvD/C,OAAA;QAAK8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/C,OAAA;UACEQ,IAAI,EAAC,MAAM;UACX6C,KAAK,EAAElD,KAAM;UACbmD,QAAQ,EAAG9B,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;UAC1CG,WAAW,EAAC,4BAA4B;UACxCC,QAAQ,EAAE7C,OAAQ;UAClB8C,SAAS;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACFnD,OAAA;UACEQ,IAAI,EAAC,QAAQ;UACbiD,QAAQ,EAAE7C,OAAO,IAAI,CAACT,KAAK,CAACuB,IAAI,CAAC,CAAE;UAAAqB,QAAA,EAElCnC,OAAO,GAAG,GAAG,GAAG;QAAI;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACjD,EAAA,CAlJuBD,cAAc;AAAA0D,EAAA,GAAd1D,cAAc;AAAA,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}