{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SearchPage() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  async function handleSearch(e) {\n    e.preventDefault();\n    setLoading(true);\n    setResults([]);\n    try {\n      const res = await fetch(\"/api/search/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query\n        })\n      });\n      const data = await res.json();\n      if (res.ok) {\n        setResults(data.matches);\n      } else {\n        alert(data.error || \"Error searching\");\n      }\n    } catch (error) {\n      alert(\"Network error\");\n    }\n    setLoading(false);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: 600,\n      margin: \"auto\",\n      padding: 20\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Weaviate Vector Search\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSearch,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: query,\n        onChange: e => setQuery(e.target.value),\n        placeholder: \"Enter your query here\",\n        required: true,\n        style: {\n          width: \"80%\",\n          padding: 8\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        style: {\n          padding: 8,\n          marginLeft: 8\n        },\n        children: loading ? \"Searching...\" : \"Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 20\n      },\n      children: results.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: results.map((item, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n          style: {\n            marginBottom: 15\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"b\", {\n            children: item.source_file\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 17\n          }, this), \" (Chunk \", item.chunk_number, \"):\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 71\n          }, this), item.content]\n        }, idx, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this) : !loading && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No results yet.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 23\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n}\n_s(SearchPage, \"jATfEewsAWhkK6SdnZsJ7dNVCAY=\");\n_c = SearchPage;\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "SearchPage", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "results", "setResults", "loading", "setLoading", "handleSearch", "e", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "matches", "alert", "error", "style", "max<PERSON><PERSON><PERSON>", "margin", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "width", "disabled", "marginLeft", "marginTop", "length", "map", "item", "idx", "marginBottom", "source_file", "chunk_number", "content", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\n\nexport default function SearchPage() {\n  const [query, setQuery] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  async function handleSearch(e) {\n    e.preventDefault();\n    setLoading(true);\n    setResults([]);\n\n    try {\n      const res = await fetch(\"/api/search/\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ query }),\n      });\n\n      const data = await res.json();\n      if (res.ok) {\n        setResults(data.matches);\n      } else {\n        alert(data.error || \"Error searching\");\n      }\n    } catch (error) {\n      alert(\"Network error\");\n    }\n\n    setLoading(false);\n  }\n\n  return (\n    <div style={{ maxWidth: 600, margin: \"auto\", padding: 20 }}>\n      <h1>Weaviate Vector Search</h1>\n      <form onSubmit={handleSearch}>\n        <input\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          placeholder=\"Enter your query here\"\n          required\n          style={{ width: \"80%\", padding: 8 }}\n        />\n        <button type=\"submit\" disabled={loading} style={{ padding: 8, marginLeft: 8 }}>\n          {loading ? \"Searching...\" : \"Search\"}\n        </button>\n      </form>\n\n      <div style={{ marginTop: 20 }}>\n        {results.length > 0 ? (\n          <ul>\n            {results.map((item, idx) => (\n              <li key={idx} style={{ marginBottom: 15 }}>\n                <b>{item.source_file}</b> (Chunk {item.chunk_number}):<br />\n                {item.content}\n              </li>\n            ))}\n          </ul>\n        ) : (\n          !loading && <p>No results yet.</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE7C,eAAeW,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,UAAU,CAAC,IAAI,CAAC;IAChBF,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMM,GAAG,GAAG,MAAMC,KAAK,CAAC,cAAc,EAAE;QACtCC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEf;QAAM,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMgB,IAAI,GAAG,MAAMP,GAAG,CAACQ,IAAI,CAAC,CAAC;MAC7B,IAAIR,GAAG,CAACS,EAAE,EAAE;QACVf,UAAU,CAACa,IAAI,CAACG,OAAO,CAAC;MAC1B,CAAC,MAAM;QACLC,KAAK,CAACJ,IAAI,CAACK,KAAK,IAAI,iBAAiB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,KAAK,CAAC,eAAe,CAAC;IACxB;IAEAf,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,oBACER,OAAA;IAAKyB,KAAK,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBACzD7B,OAAA;MAAA6B,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BjC,OAAA;MAAMkC,QAAQ,EAAEzB,YAAa;MAAAoB,QAAA,gBAC3B7B,OAAA;QACEmC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEjC,KAAM;QACbkC,QAAQ,EAAG3B,CAAC,IAAKN,QAAQ,CAACM,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;QAC1CG,WAAW,EAAC,uBAAuB;QACnCC,QAAQ;QACRf,KAAK,EAAE;UAAEgB,KAAK,EAAE,KAAK;UAAEb,OAAO,EAAE;QAAE;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACFjC,OAAA;QAAQmC,IAAI,EAAC,QAAQ;QAACO,QAAQ,EAAEnC,OAAQ;QAACkB,KAAK,EAAE;UAAEG,OAAO,EAAE,CAAC;UAAEe,UAAU,EAAE;QAAE,CAAE;QAAAd,QAAA,EAC3EtB,OAAO,GAAG,cAAc,GAAG;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPjC,OAAA;MAAKyB,KAAK,EAAE;QAAEmB,SAAS,EAAE;MAAG,CAAE;MAAAf,QAAA,EAC3BxB,OAAO,CAACwC,MAAM,GAAG,CAAC,gBACjB7C,OAAA;QAAA6B,QAAA,EACGxB,OAAO,CAACyC,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACrBhD,OAAA;UAAcyB,KAAK,EAAE;YAAEwB,YAAY,EAAE;UAAG,CAAE;UAAApB,QAAA,gBACxC7B,OAAA;YAAA6B,QAAA,EAAIkB,IAAI,CAACG;UAAW;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,YAAQ,EAACc,IAAI,CAACI,YAAY,EAAC,IAAE,eAAAnD,OAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC3Dc,IAAI,CAACK,OAAO;QAAA,GAFNJ,GAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGR,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,GAEL,CAAC1B,OAAO,iBAAIP,OAAA;QAAA6B,QAAA,EAAG;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAClC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/B,EAAA,CA/DuBD,UAAU;AAAAoD,EAAA,GAAVpD,UAAU;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}