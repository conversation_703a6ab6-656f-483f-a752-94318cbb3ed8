#!/usr/bin/env python3
"""
Startup script for AI Agent Chatbot
This script starts the backend server and opens the frontend in the browser.
"""

import subprocess
import time
import webbrowser
import os
import sys

def start_backend():
    """Start the Flask backend server."""
    print("🚀 Starting backend server...")
    try:
        # Start backend server in a separate process
        backend_process = subprocess.Popen([
            sys.executable, "backend_server.py"
        ], cwd=os.getcwd())
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        print("✅ Backend server started on http://localhost:5000")
        return backend_process
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def start_react_frontend():
    """Start the React frontend."""
    print("🌐 Starting React frontend...")
    try:
        # Start React app in a separate process
        frontend_process = subprocess.Popen([
            "npm", "start"
        ], cwd=os.path.join(os.getcwd(), "frontend"))

        # Wait a moment for the server to start
        time.sleep(5)

        # Open browser
        webbrowser.open("http://localhost:3000")
        print("✅ React frontend started on http://localhost:3000")
        return frontend_process
    except Exception as e:
        print(f"❌ Error starting React frontend: {e}")
        return None

def open_simple_frontend():
    """Open the simple HTML frontend in the default browser."""
    print("🌐 Opening simple HTML frontend in browser...")
    try:
        frontend_path = os.path.join(os.getcwd(), "simple_frontend.html")
        frontend_url = f"file:///{frontend_path.replace(os.sep, '/')}"
        webbrowser.open(frontend_url)
        print("✅ Simple HTML frontend opened in browser")
        return True
    except Exception as e:
        print(f"❌ Error opening frontend: {e}")
        return False

def main():
    """Main function to start the application."""
    print("=" * 50)
    print("🤖 AI Agent Chatbot - Vector Search")
    print("=" * 50)
    
    # Check if embeddings file exists
    embeddings_file = "embeddings_complete.json"
    if not os.path.exists(embeddings_file):
        print(f"❌ Embeddings file '{embeddings_file}' not found!")
        print("Please run 'python vector_embedding.py' first to generate embeddings.")
        return
    
    print(f"✅ Found embeddings file: {embeddings_file}")
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend server")
        return
    
    # Start React Chat frontend automatically
    print("\n🌐 Starting React Chat Interface...")
    frontend_process = start_react_frontend()
    if not frontend_process:
        print("❌ Failed to start React frontend")
        backend_process.terminate()
        return
    
    print("\n" + "=" * 50)
    print("🎉 AI Agent Chatbot Started Successfully!")
    print("=" * 50)
    print("📊 Backend API: http://localhost:5000")
    print("🌐 React Chat Interface: http://localhost:3000")
    print("=" * 50)
    print("\n💬 Chat Features:")
    print("• Conversational AI interface")
    print("• Real-time responses")
    print("• Technical documentation expertise")
    print("• Message history")
    print("\n🔍 Try asking:")
    print("• 'How do I configure the camera?'")
    print("• 'What are the installation steps?'")
    print("• 'Explain the frame rate settings'")
    print("• 'Help with troubleshooting'")
    print("\n⚠️  Press Ctrl+C to stop the application")

    try:
        # Keep the script running
        # Wait for either process to finish
        while backend_process.poll() is None and frontend_process.poll() is None:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping application...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Application stopped")

if __name__ == "__main__":
    main()
