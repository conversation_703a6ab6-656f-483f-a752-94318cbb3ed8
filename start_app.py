#!/usr/bin/env python3
"""
Startup script for AI Agent Chatbot
This script starts the backend server and opens the frontend in the browser.
"""

import subprocess
import time
import webbrowser
import os
import sys

def start_backend():
    """Start the Flask backend server."""
    print("🚀 Starting backend server...")
    try:
        # Start backend server in a separate process
        backend_process = subprocess.Popen([
            sys.executable, "backend_server.py"
        ], cwd=os.getcwd())
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        print("✅ Backend server started on http://localhost:5000")
        return backend_process
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def start_react_frontend():
    """Start the React frontend."""
    print("🌐 Starting React frontend...")
    try:
        # Start React app in a separate process
        frontend_process = subprocess.Popen([
            "npm", "start"
        ], cwd=os.path.join(os.getcwd(), "frontend"))

        # Wait a moment for the server to start
        time.sleep(5)

        # Open browser
        webbrowser.open("http://localhost:3000")
        print("✅ React frontend started on http://localhost:3000")
        return frontend_process
    except Exception as e:
        print(f"❌ Error starting React frontend: {e}")
        return None

def open_simple_frontend():
    """Open the simple HTML frontend in the default browser."""
    print("🌐 Opening simple HTML frontend in browser...")
    try:
        frontend_path = os.path.join(os.getcwd(), "simple_frontend.html")
        frontend_url = f"file:///{frontend_path.replace(os.sep, '/')}"
        webbrowser.open(frontend_url)
        print("✅ Simple HTML frontend opened in browser")
        return True
    except Exception as e:
        print(f"❌ Error opening frontend: {e}")
        return False

def main():
    """Main function to start the application."""
    print("=" * 50)
    print("🤖 AI Agent Chatbot - Vector Search")
    print("=" * 50)
    
    # Check if embeddings file exists
    embeddings_file = "embeddings_complete.json"
    if not os.path.exists(embeddings_file):
        print(f"❌ Embeddings file '{embeddings_file}' not found!")
        print("Please run 'python vector_embedding.py' first to generate embeddings.")
        return
    
    print(f"✅ Found embeddings file: {embeddings_file}")
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend server")
        return
    
    # Ask user which frontend to use
    print("\n🎯 Choose frontend:")
    print("1. React App (Modern, recommended)")
    print("2. Simple HTML (Basic, always works)")
    choice = input("Enter choice (1 or 2): ").strip()

    if choice == "1":
        # Start React frontend
        frontend_process = start_react_frontend()
        if not frontend_process:
            print("❌ Failed to start React frontend")
            backend_process.terminate()
            return
    else:
        # Open simple HTML frontend
        if not open_simple_frontend():
            print("❌ Failed to open simple frontend")
            backend_process.terminate()
            return
        frontend_process = None
    
    print("\n" + "=" * 50)
    print("🎉 Application started successfully!")
    print("=" * 50)
    print("📊 Backend API: http://localhost:5000")
    if choice == "1":
        print("🌐 React Frontend: http://localhost:3000")
    else:
        print("🌐 HTML Frontend: Opened in your default browser")
    print("=" * 50)
    print("\n💡 Usage:")
    print("1. Enter your search query in the browser")
    print("2. Click 'Search' to find similar content")
    print("3. View results with similarity scores")
    print("\n🔍 Example queries:")
    print("- 'camera configuration'")
    print("- 'frame rate settings'")
    print("- 'installation guide'")
    print("- 'troubleshooting'")
    print("\n⚠️  Press Ctrl+C to stop the application")

    try:
        # Keep the script running
        if frontend_process:
            # Wait for either process to finish
            while backend_process.poll() is None and frontend_process.poll() is None:
                time.sleep(1)
        else:
            backend_process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping application...")
        backend_process.terminate()
        if frontend_process:
            frontend_process.terminate()
        print("✅ Application stopped")

if __name__ == "__main__":
    main()
