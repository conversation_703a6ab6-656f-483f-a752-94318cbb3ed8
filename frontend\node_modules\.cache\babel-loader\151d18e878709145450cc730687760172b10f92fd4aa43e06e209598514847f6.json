{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AIAgentChatbot() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: \"bot\",\n    content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n    timestamp: new Date()\n  }]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n    testBackendConnection();\n  }, []);\n  async function handleSubmit(e) {\n    e.preventDefault();\n    setLoading(true);\n    setChatAnswer(\"\");\n    setError(\"\");\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setChatAnswer(data.answer);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n    setLoading(false);\n  }\n  const displayChatAnswer = () => {\n    if (!chatAnswer && !loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-message\",\n        children: \"\\uD83D\\uDC4B Hi! I'm your AI assistant. Ask me anything about the technical documentation!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 14\n      }, this);\n    }\n    if (chatAnswer) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-response\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"answer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDD16 AI Assistant:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"answer-content\",\n            children: chatAnswer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83E\\uDD16 AI Agent Chatbot\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"subtitle\",\n      children: \"Ask me anything about the technical documentation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"queryInput\",\n        value: query,\n        onChange: e => setQuery(e.target.value),\n        placeholder: \"Ask me anything (e.g., 'How do I configure the camera?', 'What are the installation steps?')\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        id: \"submitButton\",\n        disabled: loading,\n        children: loading ? \"🧠 Thinking...\" : \"💬 Ask AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"\\uD83E\\uDDE0 AI is thinking...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results\",\n      children: displayChatAnswer()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n}\n_s(AIAgentChatbot, \"XI0Gl11PzOvUhnDyfnhnkCRfenw=\");\n_c = AIAgentChatbot;\nvar _c;\n$RefreshReg$(_c, \"AIAgentChatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AIAgentChatbot", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "loading", "setLoading", "error", "setError", "testBackendConnection", "response", "fetch", "data", "json", "console", "log", "handleSubmit", "e", "preventDefault", "setChatAnswer", "method", "headers", "body", "JSON", "stringify", "ok", "answer", "message", "displayChatAnswer", "chatAnswer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "value", "onChange", "target", "placeholder", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport './App.css';\n\nexport default function AIAgentChatbot() {\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: \"bot\",\n      content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n      timestamp: new Date()\n    }\n  ]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n\n    testBackendConnection();\n  }, []);\n\n  async function handleSubmit(e) {\n    e.preventDefault();\n    setLoading(true);\n    setChatAnswer(\"\");\n    setError(\"\");\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ query }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setChatAnswer(data.answer);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n\n    setLoading(false);\n  }\n\n  const displayChatAnswer = () => {\n    if (!chatAnswer && !loading) {\n      return <div className=\"welcome-message\">👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!</div>;\n    }\n\n    if (chatAnswer) {\n      return (\n        <div className=\"chat-response\">\n          <div className=\"answer-section\">\n            <h3>🤖 AI Assistant:</h3>\n            <div className=\"answer-content\">\n              {chatAnswer}\n            </div>\n          </div>\n        </div>\n      );\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>🤖 AI Agent Chatbot</h1>\n      <p className=\"subtitle\">Ask me anything about the technical documentation</p>\n\n      <form className=\"chat-form\" onSubmit={handleSubmit}>\n        <input\n          type=\"text\"\n          id=\"queryInput\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          placeholder=\"Ask me anything (e.g., 'How do I configure the camera?', 'What are the installation steps?')\"\n          required\n        />\n        <button\n          type=\"submit\"\n          id=\"submitButton\"\n          disabled={loading}\n        >\n          {loading ? \"🧠 Thinking...\" : \"💬 Ask AI\"}\n        </button>\n      </form>\n\n      {error && (\n        <div className=\"error\">\n          {error}\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"loading\">\n          🧠 AI is thinking...\n        </div>\n      )}\n\n      <div className=\"results\">\n        {displayChatAnswer()}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,CACvC;IACEU,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,kFAAkF;IAC3FC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,QAAQ,CAAC,yEAAyE,CAAC;MACrF;IACF,CAAC;IAEDC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeO,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBa,aAAa,CAAC,EAAE,CAAC;IACjBX,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9DS,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE5B;QAAM,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACe,EAAE,EAAE;QACfN,aAAa,CAACP,IAAI,CAACc,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLlB,QAAQ,CAACI,IAAI,CAACL,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,QAAQ,CAAC,iBAAiB,GAAGD,KAAK,CAACoB,OAAO,CAAC;IAC7C;IAEArB,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACC,UAAU,IAAI,CAACxB,OAAO,EAAE;MAC3B,oBAAOZ,OAAA;QAAKqC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAgF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAChI;IAEA,IAAIN,UAAU,EAAE;MACd,oBACEpC,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BtC,OAAA;UAAKqC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BtC,OAAA;YAAAsC,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB1C,OAAA;YAAKqC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BF;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;EACF,CAAC;EAED,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBtC,OAAA;MAAAsC,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5B1C,OAAA;MAAGqC,SAAS,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAiD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAE7E1C,OAAA;MAAMqC,SAAS,EAAC,WAAW;MAACM,QAAQ,EAAEpB,YAAa;MAAAe,QAAA,gBACjDtC,OAAA;QACEQ,IAAI,EAAC,MAAM;QACXD,EAAE,EAAC,YAAY;QACfqC,KAAK,EAAEzC,KAAM;QACb0C,QAAQ,EAAGrB,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;QAC1CG,WAAW,EAAC,8FAA8F;QAC1GC,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF1C,OAAA;QACEQ,IAAI,EAAC,QAAQ;QACbD,EAAE,EAAC,cAAc;QACjB0C,QAAQ,EAAErC,OAAQ;QAAA0B,QAAA,EAEjB1B,OAAO,GAAG,gBAAgB,GAAG;MAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEN5B,KAAK,iBACJd,OAAA;MAAKqC,SAAS,EAAC,OAAO;MAAAC,QAAA,EACnBxB;IAAK;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA9B,OAAO,iBACNZ,OAAA;MAAKqC,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAEzB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAED1C,OAAA;MAAKqC,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBH,iBAAiB,CAAC;IAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACxC,EAAA,CApHuBD,cAAc;AAAAiD,EAAA,GAAdjD,cAAc;AAAA,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}