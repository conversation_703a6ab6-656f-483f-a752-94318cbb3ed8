# api/views.py

from rest_framework.decorators import api_view
from rest_framework.response import Response
import weaviate
from openai import OpenAI
import os

# Use environment variables for security in real projects!
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")

client_openai = OpenAI(api_key=OPENAI_API_KEY)
client_weaviate = weaviate.Client("http://localhost:8080")  # Adjust if needed
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_embedding(text):
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

@api_view(['POST'])
def search_vector(request):
    query_text = request.data.get("query", "")
    if not query_text:
        return Response({"error": "Query text required"}, status=400)

    try:
        query_embedding = get_embedding(query_text)

        result = (
            client_weaviate.query
            .get(WEAVIATE_CLASS_NAME, ["source_file", "chunk_number", "content"])
            .with_near_vector({"vector": query_embedding})
            .with_limit(5)
            .do()
        )

        matches = []
        for item in result.get("data", {}).get("Get", {}).get(WEAVIATE_CLASS_NAME, []):
            matches.append({
                "source_file": item.get("source_file"),
                "chunk_number": item.get("chunk_number"),
                "content": item.get("content"),
            })

        return Response({"matches": matches})

    except Exception as e:
        return Response({"error": str(e)}, status=500)
