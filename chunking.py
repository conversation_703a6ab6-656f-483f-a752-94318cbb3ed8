import os
import json
import fitz  # PyMuPDF
import spacy
from concurrent.futures import ThreadPoolExecutor

def load_pdf(file_path):
    """Extract text from a PDF."""
    try:
        pdf_document = fitz.open(file_path)
        text = ""
        for page in pdf_document:
            text += page.get_text()
        pdf_document.close()
        return text
    except Exception as e:
        print(f"Error loading PDF {file_path}: {e}")
        return None

def split_into_paragraphs_with_spacy(text):
    """Use SpaCy to split text into paragraphs."""
    nlp = spacy.load("en_core_web_sm")
    doc = nlp(text)
    paragraphs = []
    current_paragraph = []

    for sent in doc.sents:  # Iterate over sentences
        current_paragraph.append(sent.text.strip())
        if sent.text.endswith("\n\n") or len(current_paragraph) > 5:  # Arbitrary break logic
            paragraphs.append(" ".join(current_paragraph))
            current_paragraph = []

    if current_paragraph:
        paragraphs.append(" ".join(current_paragraph))

    return paragraphs

def dynamic_chunking(paragraphs, max_chunk_size=1000):
    """Combine paragraphs into chunks without exceeding max_chunk_size."""
    chunks = []
    current_chunk = ""
    for paragraph in paragraphs:
        if len(current_chunk) + len(paragraph) <= max_chunk_size:
            current_chunk += " " + paragraph if current_chunk else paragraph
        else:
            chunks.append(current_chunk)
            current_chunk = paragraph

    if current_chunk:
        chunks.append(current_chunk)

    return chunks

def process_single_file(file_path):
    """Process a single file to extract and chunk its content."""
    try:
        print(f"Processing file: {file_path}")
        text = load_pdf(file_path)
        if text:
            paragraphs = split_into_paragraphs_with_spacy(text)
            chunks = dynamic_chunking(paragraphs)
            return [{
                "source_file": os.path.basename(file_path),
                "chunk_number": idx + 1,
                "content": chunk
            } for idx, chunk in enumerate(chunks)]
        return []
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return []

def process_files_and_store_chunks_parallel(files, output_file="chunks.json"):
    """Process multiple files in parallel, chunk their content, and store all chunks in one file."""
    all_chunks = []
    with ThreadPoolExecutor() as executor:
        results = executor.map(process_single_file, files)
        for result in results:
            all_chunks.extend(result)

    # Save all chunks to a JSON file
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_chunks, f, indent=4, ensure_ascii=False)
    print(f"All chunks have been saved to {output_file}.")

if __name__ == "__main__":
    # Folder containing the PDF files
    pdf_directory = r"C:\Users\<USER>\Downloads\AI-Agent-Chatbot-main\MANUALS"
    pdf_files = [os.path.join(pdf_directory, f) for f in os.listdir(pdf_directory) if f.endswith('.pdf')]

    if pdf_files:
        process_files_and_store_chunks_parallel(pdf_files)
    else:
        print("No PDF files found in the directory.")
