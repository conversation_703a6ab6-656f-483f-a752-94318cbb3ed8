{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AIAgentChatbot() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [chatAnswer, setChatAnswer] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n    testBackendConnection();\n  }, []);\n  async function handleSubmit(e) {\n    e.preventDefault();\n    setLoading(true);\n    setChatAnswer(\"\");\n    setError(\"\");\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setChatAnswer(data.answer);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n    setLoading(false);\n  }\n  const displayChatAnswer = () => {\n    if (!chatAnswer && !loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"welcome-message\",\n        children: \"\\uD83D\\uDC4B Hi! I'm your AI assistant. Ask me anything about the technical documentation!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 14\n      }, this);\n    }\n    if (chatAnswer) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-response\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"answer-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDD16 AI Assistant:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"answer-content\",\n            children: chatAnswer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83E\\uDD16 AI Agent Chatbot with RAG\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"subtitle\",\n      children: \"Ask questions or search through technical documentation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mode-toggle\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `mode-btn ${mode === \"chat\" ? \"active\" : \"\"}`,\n        onClick: () => setMode(\"chat\"),\n        children: \"\\uD83D\\uDCAC AI Chat\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `mode-btn ${mode === \"search\" ? \"active\" : \"\"}`,\n        onClick: () => setMode(\"search\"),\n        children: \"\\uD83D\\uDD0D Vector Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"search-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"queryInput\",\n        value: query,\n        onChange: e => setQuery(e.target.value),\n        placeholder: mode === \"chat\" ? \"Ask me anything about the documentation (e.g., 'How do I configure the camera?')\" : \"Search for specific content (e.g., 'camera configuration', 'frame rate')\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        id: \"submitButton\",\n        disabled: loading,\n        children: loading ? mode === \"chat\" ? \"Thinking...\" : \"Searching...\" : mode === \"chat\" ? \"Ask AI\" : \"Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: mode === \"chat\" ? \"🧠 AI is thinking...\" : \"🔍 Searching...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results\",\n      children: mode === \"chat\" ? displayChatAnswer() : displaySearchResults()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}\n_s(AIAgentChatbot, \"qci3INseNRMpjWpAuojglnH4/fU=\");\n_c = AIAgentChatbot;\nvar _c;\n$RefreshReg$(_c, \"AIAgentChatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AIAgentChatbot", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "chatAnswer", "setChatAnswer", "loading", "setLoading", "error", "setError", "testBackendConnection", "response", "fetch", "data", "json", "console", "log", "handleSubmit", "e", "preventDefault", "method", "headers", "body", "JSON", "stringify", "ok", "answer", "message", "displayChatAnswer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mode", "onClick", "setMode", "onSubmit", "type", "id", "value", "onChange", "target", "placeholder", "required", "disabled", "displaySearchResults", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport './App.css';\n\nexport default function AIAgentChatbot() {\n  const [query, setQuery] = useState(\"\");\n  const [chatAnswer, setChatAnswer] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n\n    testBackendConnection();\n  }, []);\n\n  async function handleSubmit(e) {\n    e.preventDefault();\n    setLoading(true);\n    setChatAnswer(\"\");\n    setError(\"\");\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ query }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setChatAnswer(data.answer);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n\n    setLoading(false);\n  }\n\n  const displayChatAnswer = () => {\n    if (!chatAnswer && !loading) {\n      return <div className=\"welcome-message\">👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!</div>;\n    }\n\n    if (chatAnswer) {\n      return (\n        <div className=\"chat-response\">\n          <div className=\"answer-section\">\n            <h3>🤖 AI Assistant:</h3>\n            <div className=\"answer-content\">\n              {chatAnswer}\n            </div>\n          </div>\n        </div>\n      );\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>🤖 AI Agent Chatbot with RAG</h1>\n      <p className=\"subtitle\">Ask questions or search through technical documentation</p>\n\n      {/* Mode Toggle */}\n      <div className=\"mode-toggle\">\n        <button\n          className={`mode-btn ${mode === \"chat\" ? \"active\" : \"\"}`}\n          onClick={() => setMode(\"chat\")}\n        >\n          💬 AI Chat\n        </button>\n        <button\n          className={`mode-btn ${mode === \"search\" ? \"active\" : \"\"}`}\n          onClick={() => setMode(\"search\")}\n        >\n          🔍 Vector Search\n        </button>\n      </div>\n\n      <form className=\"search-form\" onSubmit={handleSubmit}>\n        <input\n          type=\"text\"\n          id=\"queryInput\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          placeholder={\n            mode === \"chat\"\n              ? \"Ask me anything about the documentation (e.g., 'How do I configure the camera?')\"\n              : \"Search for specific content (e.g., 'camera configuration', 'frame rate')\"\n          }\n          required\n        />\n        <button\n          type=\"submit\"\n          id=\"submitButton\"\n          disabled={loading}\n        >\n          {loading ? (mode === \"chat\" ? \"Thinking...\" : \"Searching...\") : (mode === \"chat\" ? \"Ask AI\" : \"Search\")}\n        </button>\n      </form>\n\n      {error && (\n        <div className=\"error\">\n          {error}\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"loading\">\n          {mode === \"chat\" ? \"🧠 AI is thinking...\" : \"🔍 Searching...\"}\n        </div>\n      )}\n\n      <div className=\"results\">\n        {mode === \"chat\" ? displayChatAnswer() : displaySearchResults()}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,QAAQ,CAAC,yEAAyE,CAAC;MACrF;IACF,CAAC;IAEDC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeO,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBF,aAAa,CAAC,EAAE,CAAC;IACjBI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9DQ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEtB;QAAM,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMW,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACc,EAAE,EAAE;QACfpB,aAAa,CAACQ,IAAI,CAACa,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLjB,QAAQ,CAACI,IAAI,CAACL,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,QAAQ,CAAC,iBAAiB,GAAGD,KAAK,CAACmB,OAAO,CAAC;IAC7C;IAEApB,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAMqB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACxB,UAAU,IAAI,CAACE,OAAO,EAAE;MAC3B,oBAAOP,OAAA;QAAK8B,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAgF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAChI;IAEA,IAAI9B,UAAU,EAAE;MACd,oBACEL,OAAA;QAAK8B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B/B,OAAA;UAAK8B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/B,OAAA;YAAA+B,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnC,OAAA;YAAK8B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5B1B;UAAU;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;EACF,CAAC;EAED,oBACEnC,OAAA;IAAK8B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/B,OAAA;MAAA+B,QAAA,EAAI;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrCnC,OAAA;MAAG8B,SAAS,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAuD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAGnFnC,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B/B,OAAA;QACE8B,SAAS,EAAE,YAAYM,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;QACzDC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,MAAM,CAAE;QAAAP,QAAA,EAChC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnC,OAAA;QACE8B,SAAS,EAAE,YAAYM,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC3DC,OAAO,EAAEA,CAAA,KAAMC,OAAO,CAAC,QAAQ,CAAE;QAAAP,QAAA,EAClC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENnC,OAAA;MAAM8B,SAAS,EAAC,aAAa;MAACS,QAAQ,EAAErB,YAAa;MAAAa,QAAA,gBACnD/B,OAAA;QACEwC,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,YAAY;QACfC,KAAK,EAAEvC,KAAM;QACbwC,QAAQ,EAAGxB,CAAC,IAAKf,QAAQ,CAACe,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;QAC1CG,WAAW,EACTT,IAAI,KAAK,MAAM,GACX,kFAAkF,GAClF,0EACL;QACDU,QAAQ;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnC,OAAA;QACEwC,IAAI,EAAC,QAAQ;QACbC,EAAE,EAAC,cAAc;QACjBM,QAAQ,EAAExC,OAAQ;QAAAwB,QAAA,EAEjBxB,OAAO,GAAI6B,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,cAAc,GAAKA,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG;MAAS;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEN1B,KAAK,iBACJT,OAAA;MAAK8B,SAAS,EAAC,OAAO;MAAAC,QAAA,EACnBtB;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA5B,OAAO,iBACNP,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBK,IAAI,KAAK,MAAM,GAAG,sBAAsB,GAAG;IAAiB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACN,eAEDnC,OAAA;MAAK8B,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBK,IAAI,KAAK,MAAM,GAAGP,iBAAiB,CAAC,CAAC,GAAGmB,oBAAoB,CAAC;IAAC;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjC,EAAA,CAjIuBD,cAAc;AAAAgD,EAAA,GAAdhD,cAAc;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}