[{"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 1, "content": "Falcon™ 4-CLHS Series \nCamera User’s Manual \nCLHS True High Performance Area Scan \nsensors | cameras | frame grabbers | processors | software | vision solutions \nP/N: 03-032-20295-07 \nwww.teledynedalsa.com \n \n \nThis document does not contain information whose export/transfer/disclosure  \nis restricted by the Canadian Export Control regulation. Notice \n© 2021-2023 Teledyne Digital Imaging, Inc. Teledyne DALSA believes all information provided in this manual to be accurate and reliable. Teledyne DALSA \nassumes no responsibility for its use. We reserve the right to make changes to this information without notice. We \nprohibit the reproduction of this manual in whole or in part by any means without prior permission obtained from \nTeledyne DALSA."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 2, "content": "Microsoft and Windows are registered trademarks of Microsoft Corporation in the United States and other \ncountries. Windows, Windows 10 are trademarks of Microsoft Corporation. All other trademarks or intellectual properties mentioned herein belong to their respective owners. Document Date: 2023-12-18 \n \nAbout Teledyne DALSA \nTeledyne DALSA, a business unit of Teledyne Digital Imaging Inc., is an international high-performance \nsemiconductor and Electronics Company that designs, develops, manufactures, and markets digital imaging \nproducts and solutions, in addition to providing wafer foundry services. Teledyne DALSA offers the widest range of machine vision components in the world. From industry-leading \nimage sensors through powerful and sophisticated cameras, frame grabbers, vision processors and software \neasy-to-use vision appliances and custom vision modules."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 3, "content": "Contact Teledyne DALSA \nTeledyne DALSA’s headquarters are in Waterloo, Ontario, Canada. We have sales offices in the USA, Europe \nand Asia, plus a worldwide network of representatives and agents to serve you efficiently. Contact information for sales, support inquiries and directions to our offices are found on our web site: \nSales Offices \nhttps://www.teledynedalsa.com/en/contact/contact-sales/ \nTechnical Support \nhttps://www.teledynedalsa.com/en/support/options/ \n \n \n \n \nFalcon™ 4-CLHS Series \nContents  •  iii \nContents \nSERIES OVERVIEW ___________________________________________________________ 1 \nDESCRIPTION ............................................................................................................................... 1 \nFalcon4-CLHS Overview ..................................................................................................... 1 \nMODEL PART NUMBERS ................................................................................................................ 2 \nMonochrome Cameras ........................................................................................................"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 4, "content": "2 \nSupported Teledyne DALSA Frame Grabbers .................................................................... 2 \nCamera Firmware ................................................................................................................ 2 \nAccessories ......................................................................................................................... 3 \nHARDWARE AND SOFTWARE ENVIRONMENTS ................................................................................. 4 \nMounting .............................................................................................................................. 4 \nFrame Grabbers and Cabling .............................................................................................. 4 \nSoftware Platforms .............................................................................................................. 4 \nDevelopment Software for Camera Control ........................................................................ 4 \nFALCON4-CLHS SPECIFICATIONS ______________________________________________ 5 \nCOMMON SPECIFICATIONS ............................................................................................................ 5 \nSensor Cosmetic Specifications .......................................................................................... 6 \nFALCON4-CLHS SPECIFICATIONS: M4480, M4400, M2240 .......................................................... 7 \nQuantum Efficiency Curves M2240, M4400, M4480 ........................................................... 8 \nSpectral Responsivity ................................................................................................................. 8 \nEffective Quantum Efficiency ...................................................................................................... 8 \nFALCON4-CLHS SPECIFICATIONS: M6200, M8200 ....................................................................... 9 \nQuantum Efficiency Curves M6200, M8200 ...................................................................... 10 \nSpectral Responsivity ............................................................................................................... 10 \nEffective Quantum Efficiency .................................................................................................... 10 \nINSTALLATION _____________________________________________________________ 11 \nREQUIREMENTS.......................................................................................................................... 11 \nFrame Grabber and Cables ............................................................................................... 11 \nCamera Link HS Cables ........................................................................................................... 11 \nCamera Power .......................................................................................................................... 11 \nSoftware, firmware, and device driver downloads ............................................................. 12 \nQUICK START (USING A TELEDYNE DALSA FRAME GRABBER)...................................................... 13 \nINSTALLATION DETAILS ............................................................................................................... 14 \nSapera LT Installation ........................................................................................................ 14 \nBoard Driver Installation ...................................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 5, "content": "14 \nTESTING ACQUISITION ................................................................................................................ 15 \nStart CamExpert ................................................................................................................ 15 \nUpload Camera Firmware ................................................................................................. 15 \nVerify Basic Acquisition ..................................................................................................... 15 \nFALCON4-CLHS CONNECTORS AND STATUS LED ....................................................................... 18 \nConnectors ........................................................................................................................ 18 \nLED Indicators ................................................................................................................... 18 \nCamera Status LED Indicator ................................................................................................... 18 \nPREVENTING OPERATIONAL FAULTS DUE TO ESD ........................................................................ 19 \nOPERATIONAL REFERENCE __________________________________________________ 20 \nUSING CAMEXPERT WITH FALCON4-CLHS .................................................................................. 20 \nCamExpert Panes ............................................................................................................. 20 \nCamExpert View Parameters Option ........................................................................................ 21 \nCAMERA FEATURE CATEGORIES ................................................................................................. 22 \nCAMERA INFORMATION CATEGORY .............................................................................................. 23 \nCamera Information Feature Descriptions ........................................................................ 23 \nPower-up Configuration Dialog.......................................................................................... 26 \n \n \niv  •  Contents \nFalcon™ 4-CLHS Series \nCamera Power-up Configuration .............................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 6, "content": "26 \nLoad / Save Configuration ........................................................................................................ 26 \nCAMERA CONTROL CATEGORY ................................................................................................... 27 \nCamera Control Feature Descriptions ............................................................................... 27 \nLong Exposure Mode, Time Exposure, Fast Readout Mode, and Gain ........................... 29 \nDIGITAL IO CONTROL CATEGORY ................................................................................................ 30 \nDigital IO Control Feature Descriptions ............................................................................. 30 \nI/O Module Block Diagram ........................................................................................................ 33 \nTrigger Mode Details ................................................................................................................ 33 \nTrigger Source Types (Trigger Mode = On) .............................................................................. 33 \nTrigger Overlap: Feature Details .............................................................................................. 34 \nDATA PROCESSING CATEGORY ................................................................................................... 36 \nData Processing Feature Descriptions .............................................................................. 36 \nFPN Correction .................................................................................................................. 37 \nPerforming an FPN Calibration via Sapera CamExpert ............................................................ 38 \nDefective Pixel Replacement ............................................................................................. 41 \nExample User Defective Pixel Map XML File ..........................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 7, "content": "41 \nDefective Pixel Replacement Algorithm (M2240, M4400, M4480) ............................................ 42 \nDefective Pixel Replacement Algorithm (M6200, M8200) ........................................................ 42 \nFLAT FIELD CATEGORY ............................................................................................................... 44 \nFlat Field Feature Descriptions.......................................................................................... 44 \nLENS SHADING CORRECTION CATEGORY .................................................................................... 46 \nLens Shading Correction Feature Descriptions ................................................................. 46 \nLens Shading Calibration .................................................................................................. 47 \nLUT CATEGORY ......................................................................................................................... 48 \nLUT Feature Description ................................................................................................... 48 \nLookup Table (LUT) Overview ........................................................................................... 49 \nLUT Size vs. Pixel Format ........................................................................................................ 49 \nIMAGE FORMAT CONTROL CATEGORY ......................................................................................... 50 \nImage Format Control Feature Description ......................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 8, "content": "50 \nWidth and Height Features for Partial Scan Control ......................................................... 52 \nVertical Cropping (Partial Scan) ............................................................................................... 52 \nMaximum Frame Rate Examples ............................................................................................. 53 \nHorizontal Cropping (Partial Scan) ........................................................................................... 54 \nMultiple ROIs ..................................................................................................................... 55 \nBinning Function ................................................................................................................ 56 \nInternal Test Pattern Generator ......................................................................................... 57 \nTRANSPORT LAYER CATEGORY ................................................................................................... 58 \nTransport Layer Feature Descriptions ..............................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 9, "content": "58 \nACQUISITION AND TRANSFER CONTROL CATEGORY ..................................................................... 59 \nAcquisition and Transfer Feature Descriptions ................................................................. 59 \nAcquisition Buffering ................................................................................................................. 60 \nFeatures that cannot be changed during a Transfer ......................................................... 60 \nDEVICE COUNTER AND TIMER CONTROL CATEGORY .................................................................... 61 \nDevice Counter and Timer Control Feature Descriptions ................................................. 61 \nCounter and Timer Group Block Diagram ................................................................................ 65 \nExample: Counter Start Source = OFF ..................................................................................... 65 \nExample: Counter Start Source = CounterEnd (itself) .............................................................. 66 \nExample: CounterStartSource = EVENT and Signal (Edge Base) ..........................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 10, "content": "66 \nExample: CounterStartSource = Line (Edge Base) Example ................................................... 67 \nCYCLING PRESET CATEGORY ..................................................................................................... 68 \nCycling Preset Mode Feature Description ......................................................................... 68 \nUsing Cycling Presets—a Simple Example ....................................................................... 71 \nMulti-Exposure Cycling Example Setup .................................................................................... 71 \nCycling Reset Timing Details ............................................................................................. 72 \nCase 1: Cycling with Internal Synchronous Increment ............................................................. 72 \nCase 2: Cycling with External Asynchronous Increment .......................................................... 72 \nUsing Cycling Presets with Output Controls ...................................................................... 73 \nFeature Settings for this Example ............................................................................................. 73 \nCycling Presets with Multiple ROIs...................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 11, "content": "74 \n \n \nFalcon™ 4-CLHS Series \nContents  •  v \nCycling Presets with Lens Shading Correction ................................................................. 75 \nMETADATA CONTROLS CATEGORY .............................................................................................. 76 \nMetadata Controls Feature Description ............................................................................. 76 \nExtracting Metadata Stored in a Sapera Buffer ................................................................. 77 \nMetadata Structure ............................................................................................................ 77 \nDigital Gain (raw) ............................................................................................................... 79 \nAnalog Gain (raw) .............................................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 12, "content": "79 \nFILE ACCESS CONTROL CATEGORY ............................................................................................ 80 \nFile Access Control Feature Descriptions ......................................................................... 80 \nUpdating Firmware via File Access in CamExpert ............................................................ 82 \nIMPLEMENTING TRIGGER-TO-IMAGE RELIABILITY _______________________________ 83 \nOVERVIEW ................................................................................................................................. 83 \nT2IR with Falcon4-CLHS ................................................................................................... 83 \nFEATURES FOR T2IR MONITORING .............................................................................................. 84 \nTECHNICAL SPECIFICATIONS ________________________________________________ 85 \nFALCON4-CLHS IDENTIFICATION AND MECHANICAL NOTES .......................................................... 85 \nTemperature Management ................................................................................................ 85 \nMECHANICAL SPECIFICATIONS WITH M42 MOUNT ........................................................................ 86 \nSENSOR ALIGNMENT SPECIFICATION ........................................................................................... 88 \nCONNECTORS ............................................................................................................................ 89 \nCamera Link HS (CX4) ...................................................................................................... 89 \n10-pin I/O Connector Details ............................................................................................. 89 \nPinout Details for FA-HM00-M4485 .......................................................................................... 90 \nPinout Details for FA-HM10-M2245, FA-HM11-M4405, FA-HM10-M4485,  FA-HM10-M6205, \nFA-HM10-M8205 ............................................................................................................... 90 \nCamera DC Power Characteristics ........................................................................................... 90 \nI/O Mating Connector Specifications & Sources ....................................................................... 91 \nInput Signals Electrical Specifications ............................................................................... 93 \nExternal Inputs Block Diagram ................................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 13, "content": "93 \nExternal Input Details ............................................................................................................... 93 \nExternal Input DC Characteristics ............................................................................................. 93 \nExternal Input AC Timing Characteristics ................................................................................. 94 \nExternal Inputs: Using TTL/LVTTL Drivers ............................................................................... 94 \nExternal Inputs: Using Common Collector NPN Drivers ........................................................... 95 \nExternal Inputs: Using Common Emitter NPN Driver ................................................................ 95 \nExternal Inputs: Using a Balanced Driver ................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 14, "content": "96 \nOutput Signals Electrical Specifications ............................................................................ 97 \nExternal Outputs Block Diagram ............................................................................................... 97 \nExternal Output Details and DC Characteristics ....................................................................... 97 \nExternal Output AC Timing Characteristics .............................................................................. 98 \nOutput Characteristics, FA-HM00-M4485 ................................................................................. 98 \nOutput Characteristics, FA-HM10-M2245, FA-HM11-M4405, FA-HM10-M4485, FA-HM10-\nM8205 ............................................................................................................................... 99 \nExternal Outputs: Using External TTL/LVTTL Drivers ............................................................ 100 \nExternal Outputs: Using External LED Indicators ................................................................... 100 \nUsing Falcon4 Outputs to drive other Falcon4 Inputs ............................................................. 101 \nDECLARATIONS OF CONFORMITY ____________________________________________ 103 \nFCC Statement of Conformance ..................................................................................... 103 \nFCC Class A Product ............................................................................................................. 103 \nCE Declaration of Conformity .......................................................................................... 103 \nADDITIONAL REFERENCE INFORMATION ______________________________________ 104 \nCHOOSING A LENS WITH THE CORRECT IMAGE CIRCLE ..............................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 15, "content": "104 \nLens Options for M2240, M4400 and M4480 .................................................................. 104 \nLens Options for M6200 and M8200 ............................................................................... 105 \nAdditional Lens Parameters (application specific)........................................................... 106 \nOPTICAL CONSIDERATIONS ....................................................................................................... 107 \n \n \nvi  •  Contents \nFalcon™ 4-CLHS Series \nIllumination ....................................................................................................................... 107 \nLight Sources ................................................................................................................... 107 \nBack Focal Variance when using any Filter .................................................................... 107 \nLens Modeling ................................................................................................................. 109 \nMagnification and Resolution .......................................................................................... 109 \nSENSOR HANDLING INSTRUCTIONS ............................................................................................"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 16, "content": "110 \nElectrostatic Discharge and the Sensor .......................................................................... 110 \nProtecting Against Dust, Oil and Scratches .................................................................... 110 \nCleaning the Sensor Window .......................................................................................... 111 \nI/O CABLE ACCESSORIES ......................................................................................................... 112 \nCable Manufacturers Contact Information ....................................................................... 112 \nCable Assembly G5-AIOC-BLUNT2M ............................................................................. 113 \nGeneric Power Supply with no I/O .................................................................................. 115 \nTROUBLESHOOTING _______________________________________________________ 116 \nOVERVIEW ............................................................................................................................... 116 \nProblem Type Summary .................................................................................................. 116 \nBefore Contacting Technical Support ....................................................................................."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 17, "content": "116 \nDEVICE AVAILABLE WITH OPERATIONAL ISSUES ......................................................................... 116 \nFirmware Updates ........................................................................................................... 116 \nPower Failure During a Firmware Update–Now What? .................................................. 117 \nCabling and Communication Issues ................................................................................ 117 \nCamera is Functional, Frame Rate is as Expected, but Image is Black ......................... 117 \nREVISION HISTORY ________________________________________________________ 118 \nCONTACT INFORMATION ___________________________________________________ 119 \nSALES INFORMATION ................................................................................................................"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 18, "content": "119 \nTECHNICAL SUPPORT ............................................................................................................... 119 \n \n \nFalcon™ 4-CLHS Series \nSeries Overview  •  1 \nSeries Overview \nDescription \nThe Falcon4-CLHS series provides affordable easy to use digital cameras specifically engineered for industrial \nimaging applications, starting with the industry’s latest leading sensors such as the E2V Lince 11M and \nE2V Emerald 67M series of global shutter high frame rate CMOS sensors. Cameras are available in a number of \nmodels implementing different sensors, image resolutions and feature sets. Falcon4-CLHS supports the Teledyne DALSA Trigger-to-Image-Reliability framework to dependably capture and \ntransfer images from the camera to the host PC. Falcon4-CLHS Overview \n• \nCLHS 10 Gbps interface per lane \n• \nSupports a power supply input of 10 to 30 VDC  \n• \nSupports the CLHS device discovery methodology providing plug and play capability \n• \nImplements GenICam and associated GenCP compatible with Teledyne DALSA or third party CLHS frame \ngrabbers \n• \nAcquisitions in 8 or 10-bit \n• \nOptimized, rugged design with a wide operating temperature range  \n• \nAvailable in multiple models of different resolutions and maximum frame rates  \n• \nVisual camera multicolor status LED on back plate \n• \nUses one CX4 cable connection \n• \nFlexible general-purpose Counter and Timer functions available for internal and external controls \n• \nDefective Pixel Replacement and Flat Field Correction available \n• \nLens Shading Correction maps for lens vignetting  \n• \nHorizontal and Vertical Binning \n• \nUp to 32 ROIs (regions of interest) \n• \nApplication development with the freely available Sapera™ LT software libraries \n• \nNative Teledyne DALSA Trigger-to-Image Reliability design framework \n• \nCompliant with Camera Link HS (CLHS) specification version 1.0 (X-Protocol) \n(visit www.automate.org/vision for details on industry standards)  \n \n \n2  •  Series Overview \nFalcon™ 4-CLHS Series \nModel Part Numbers  \nThis manual covers the released Falcon4-CLHS monochrome models summarized in the table below. See \nFalcon4-CLHS Specifications for details."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 19, "content": "Monochrome Cameras  \n \nFalcon4 Model \nFull Resolution \nSensor / max FPS \nData \nFormat \nLens \nMount \nPart Number \nM2240 \n2240 x 1248 \nE2V Lince 2.8M (proprietary) \n(1200 fps) \n8-bit or 10-bit \nMono \nM42 mount \nFA-HM10-M2245 \nM4400 \n4480 x 2496 \nE2V Lince 11M  \n(330 fps) \n8-bit or 10-bit \nMono \nM42 mount \nFA-HM11-M4405 \nM4480 \n4480 x 2496 \nE2V Lince 11M  \n(600 fps) \n8-bit or 10-bit \nMono \nM42 mount \nFA-HM00-M4485 \nFA-HM10-M4485 \nM6200 \n6144 x 6144 \nE2V Emerald 37M  \n(120 fps) \n8-bit or 10-bit \nMono \nM42 mount \nFA-HM10-M6205 \nM8200 \n8192 x 8192 E2V Emerald 67M  \n(90 fps) \n8-bit or 10-bit \nMono \nM42 mount \nFA-HM10-M8205 \nSupported Teledyne DALSA Frame Grabbers \n \nFalcon4 Model \nTeledyne DALSA Frame Grabber \nPart Number \nM2240, M4400 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \nXtium2 CLHS PX8 LC \nOR-A8S0-PX840 \nM4480, M6200, M8200 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \n \nCamera Firmware \nTeledyne DALSA Falcon4-CLHS camera firmware contains open-source software provided under different open-\nsource software licenses. More information about these open-source licenses can be found in the documentation \nthat accompanies the firmware, which is available on the Teledyne DALSA website at www.teledynedalsa.com. Firmware updates are available for download from the Teledyne DALSA web site \nwww.teledynedalsa.com/en/support/downloads-center. When using Sapera LT, update the camera firmware using CamExpert (see Updating Firmware via File Access in \nCamExpert). The firmware can also be easily upgraded within your own application via the API."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 20, "content": "The camera has \na failsafe scheme which prevents unrecoverable camera errors even in the case of a power interruption. Falcon™ 4-CLHS Series \nSeries Overview  •  3 \nAccessories \n \nFalcon4 Accessories & Cables (sold separately) Order Number \nM42 x 1 mm to F-mount (Nikon) lens adapter \n \nNote that there is no support for Nikon lens features such as \nfocus and aperture motor controls. G2-AM42-MOUNT4 \nPower Supply \n \nGeneric 12 V power supply to camera aux. connector \n(Samtec ISDF-05-D connector 10-pin) 4 m length \n \nG3-APWS-S10S04M \nCable \n \nCamera auxiliary Samtec ISDF-05-D connector to flying \nleads (open-ended) from pin 1 to 10, 2 m length \n \nG5-AIOC-BLUNT2M \nHeatsink  \n \nCompatible to Falcon4 casing \n51 mm x 28 mm x 15 mm (screws included) \n \nG3-AHSK-51X28 \nMounting Bracket Plate \n \nWith 1/4 inch screw mount (tripod mount)  \n35 mm length \n \nG3-AMNT-BRA02 \nActive Optical Cable (AOC) cable 10 meters,  \nwith screw lock CX4 connector \n \nAC-CA-00007-00-R \n \n \n \n4  •  Series Overview \nFalcon™ 4-CLHS Series \nHardware and Software Environments The following describes suggested hardware and supported software for successful imaging systems using the \nFalcon4-CLHS."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 21, "content": "Mounting \nThe camera requires a mounting platform which includes camera heatsinking. Thermal management and heat \ndissipation is mandatory to ensure the camera remains within the stated operating temperature specification. See \nsection Mechanical Specifications with M42 Mount for the location of the camera mounting screw holes. Frame Grabbers and Cabling \nA Teledyne DALSA frame grabber, such as the Xtium2-CLHS PX8, is recommended for error free acquisitions \nwith the Falcon4 camera (contact sales for additional information). See Frame Grabber and Cables. See Cable Manufacturers Contact Information for contact information for information on CLHS CX4 cable \nsuppliers and various I/O assemblies for your imaging solution."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 22, "content": "Software Platforms  \n \nPlatform  \nNotes \nSupport of GenICam GenCP  \nCamera setting, acquisition and other controls \nSupport of GenICam File access implementation \nFile access support for firmware update  \nSupport of GenICam XML schema version 1.1  \n \nGenICam™ support — XML camera description file  \nEmbedded within Falcon4 \n \nDevelopment Software for Camera Control \n \nTeledyne DALSA Software Platform for Microsoft Windows \nSapera LT for Windows —  \n• M2240, M4400, M4480: version 8.6 or later \n• M6200, M8200: version 8.70 or later \nIncludes Sapera Runtime and CamExpert. Provides everything you will need to develop imaging applications  \nSapera documentation provided in compiled HTML help  \nand Adobe Acrobat® (PDF) Available for download  \nhttps://www.teledynedalsa.com/en/products/imaging/vision-\nsoftware/sapera-lt/download/  \nThird Party Software Platforms \nGenICam GenCP Compliant Software and Tools \nContact your supplier \n \n \n \n \nFalcon™ 4-CLHS Series \nFalcon4-CLHS Specifications  •  5 \nFalcon4-CLHS Specifications \nCommon specifications, model-specific functional features and timing details are listed here. Common Specifications \nCamera Controls \n \nCommunication Protocol \nGenCP (GenICam GenCP compliant software), CLHS X Protocol \nSynchronization Modes \nFree running, Triggered (via CX4 cable or external I/O) Exposure Control \nInternal – Programmable via the camera API \nExternal – Timed Trigger or Trigger Width modes supported via I/O \nExposure Time Minimum / Maximum \n5 µs – 0.5 s (M2240, M4400, M4480) \n8 µs – 0.5 s (M6200, M8200)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 23, "content": "Exposure Modes \nTimed: \nProgrammable in increments of 1µs (minimum time (in µs) is model specific) Trigger Width: Pulse controlled via external Trigger pulse width  \nInput / Output Ports \n2 input / 3 output ports (FA-HM00-M4485), opto-coupled \n2 input / 4 output ports (FA-HM10-M2245, FA-HM11-M4405, FA-HM10-M4485, FA-HM10-M6205, \nFA-HM10-M8205), opto-coupled \nFeatures \n \nGain  \nIn-sensor gain (sensor-specific), digital gain \nDefective Pixel Replacement \nUp to 1022 entries  \nBinning* \n2 x 2, horizontal and vertical (not available in M2240)  \nLens Shading Correction \n1 user coefficient set \nLUT* \nProgrammable LUT (lookup table) up to 10-bit \nCounter and Timer \n1 Counter and 1 Timer. User programmable, acquisition independent, with event generation, and \ncan control Output I/O pins \nTest image \nInternal generator with choice of static patterns \nMetadata*   \nMetadata output at the end of the images (also known as GenICam Chunk Data) \nCycling Mode* \nAutomatic cycling between 64 camera setups  \nMulti ROI* \nMultiple regions of interest. Max ROIs: 32 without cycling, 8 with cycling (not available in M2240) User settings \nSelect factory default or either of two user saved camera configurations \n \n \nCLHS Link Speed \n10.3 Gbps per Lane \n4 Data Lanes (FA-HM10-M2245, FA-HM11-M4405) \n7 Data Lanes (FA-HM00-M4485, FA-HM10-M4485, FA-HM10-M6205, FA-HM10-M8205)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 24, "content": "Back Focal Distance \n \n \n12 mm (with M42 lens mount) \nOptical Interface \n \nSensor Alignment (Relative to sides of camera) Flatness \n50 µm \n y \n200 µm (Parallelism vs. front plate) \nx \n± 100 µm (Cross-Scan Direction) \ny \n± 100 µm (In-Scan Direction) \nz \n± 300 µm (Along optical axis) \n z \n± 1.0° (Rotation around optical axis) * Will be available in future release for models M6200, M8200. \n \n \n6  •  Falcon4-CLHS Specifications \nFalcon™ 4-CLHS Series \nMechanical Interface \n \nCamera (H x W x L) \nsee Mechanical Specifications  \n59 mm x 59 mm x 74.5 mm (M2240, M4400, M4480) \n59 mm x 59 mm x 74.93 mm (M6200, M8200) Mass  \nSmall Case: ~ 317g  \nCX4 Connector Type \nCamera Link HS  \nPower connector \nCamera power via the power pins on the 10-pin I/O connector  \nElectrical Interface \n \nInput Voltage  \n+24 V nominal (+10 to +30 V DC maximum range)  \nPower Dissipation (typical) \n<13 W @ 24 V DC (M2240, M4400, M4480) <15 W @ 24 V DC (M6200, M8200) \nEnvironmental Conditions \n \nOperating Temperature \n(at camera front plate) \nAll Models: -20 ° C to +50 °C (-4 °F to +122 °F)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 25, "content": "Any metallic camera mounting provides heat \nsinking, thereby reducing the internal temperature  \nOperating Relative Humidity \n5% to 90% non-condensing  \nStorage  \n-40 °C to +80 °C (-40 °F to +176 °F) temperature at 20% to 80% non-condensing relative humidity \nConformity \nGenIcam compliant  \nCE, FCC, RoHS, KC  \nEU RoHS2, EU REACH, China RoHS2 \nIP Rating  \nIEC60529 IP40  \n(Protected against foreign object of 1 mm diameter, no protection against water.) Sensor Cosmetic Specifications After Factory Calibration and/or Corrections are applied (if applicable — dependent on sensor) \nBlemish Specifications \nMaximum Number of \nDefects \nBlemish Description  \nHot/Dead Pixel defects \nTypical 0.0025% \nMax 0.005% \nAny pixel that deviates by ±15% from the average of neighboring pixels at \n80% saturation including pixel stuck at 0 and maximum saturated value. Spot defects \nnone \nGrouping of more than 5 pixel defects within a sub-area of 3x3 pixels, to a \nmaximum spot size of 5x5 pixels. Clusters defects \nnone \nGrouping of more than 5 single pixel defects in a 3x3 kernel. Column defects \nnone \nVertical grouping of more than 10 contiguous pixel defects along a single \ncolumn."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 26, "content": "Row defects \nnone \nHorizontal grouping of more than 10 contiguous pixel defects along a \nsingle row. Test conditions \n• \nNominal light = illumination at 80% of saturation \n• \nTemperature of camera is 45°C  \n• \nAt exposures lower than 0.25 seconds  \n• \nAt nominal sensor gain (1x) \n \n \nFalcon™ 4-CLHS Series \nFalcon4-CLHS Specifications  •  7 \nFalcon4-CLHS Specifications: M4480, M4400, M2240 \n \nSupported Features \nM4480 \nM4400 \nM2240 \nResolution \n4480 x 2496 \n4480 x 2496 \n2240 x 1248 \nSensor \nE2V Lince 11M \nE2V Lince 11M \nE2V Lince 2.8M (Proprietary) Pixel Size \n6 µm x 6 µm \n6 µm x 6 µm \n12 µm x 12 µm \nShutter Type  \nFull frame electronic global \nshutter function \nFull frame electronic global \nshutter function \nFull frame electronic global \nshutter function \nFull Well Charge  \n> 38 ke– \n> 38 ke– \n> 142 ke– \nMaximum Frame Rate (8-bit) \n600 fps \n330 fps \n1206 fps \nCLHS configuration (X-Protocol) \n7-Lanes \n4-Lanes \n4-Lanes \nPixel Format (Mono) \n \nMonochrome 8 or 10-bit \nMonochrome 8 or 10-bit \nMonochrome 8 or 10-bit \nSensor Synchronization \nSynchronous mode via external \ntrigger signal or free running \nSynchronous mode via external \ntrigger signal or free running \nSynchronous mode via external \ntrigger signal or free running \nTrigger to Exposure Minimum \nDelay (Synchronous Exposure) \n9 µs (8-bit) \n9 µs (8-bit) \n9 µs (8-bit)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 27, "content": "Trigger to Exposure Minimum \nDelay (Reset Exposure) \n9 µs (8-bit) \n9 µs (8-bit) \n9 µs (8-bit) Trigger to Exposure Start Jitter \n(Synchronous Exposure) \nUp to 1 line time \nUp to 1 line time \nUp to 1 line time \nTrigger to Exposure Start Jitter  \n(Reset Exposure) \n0 µs \n0 µs \n0 µs \nExposure Time Minimum \n(see exposureTimeActual) \n5 µs \n5 µs \n5 µs \nHorizontal Line Time \n0.655 µs \n0.655 µs \n0.655 µs \nMin. Time from End of \nExposure to Start of Next \nExposure  \n4.47 µs \n4.47 µs \n4.47 µs \nReadout Time (full frame size) Number of rows must be a multiple of 8 \n  8-bit / fast readout active:  \n20.3e-9*(128)*(number of rows+16) / 4+20.3e-9*(128)   (M4480 ONLY) \n  8-bit / fast readout off:  \n20.3e-9*(137)*(number of rows+16) / 4+20.3e-9*(137) \n10-bit / fast readout active:  \n20.3e-9*(154)*(number of rows+16) / 4+20.3e-9*(154)   (M4480 ONLY) \n10-bit / fast readout off:  \n20.3e-9*(165)*(number of rows+16) / 4+20.3e-9*(165) \nBlack Offset Control Yes (in DN) Yes (in DN)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 28, "content": "Yes (in DN) \nGain Control \nIn-sensor Analog Gain (1x to 4x), \nFPGA Digital Gain \nIn-sensor Analog Gain (1x to 4x), \nFPGA Digital Gain \nIn-sensor Analog Gain (1x to 4x), \nFPGA Digital Gain \nDefective Pixel Replacement \nYes, up to 1022 pixel positions Yes, up to 1022 pixel positions Yes, up to 1022 pixel positions \nImage Correction \nLens Shading Correction  \n(4 user sets) \nFPN Correction  \n(Factory + 2 user sets) Lens Shading Correction  \n(4 user sets) FPN Correction  \n(Factory + 2 user sets) \nLens Shading Correction  \n(4 user sets) FPN Correction  \n(Factory + 2 user sets) \nMulti-ROI Support \nYes"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 29, "content": "Yes \nNo \nOutput Dynamic Range (dB)  \n> 59.5 \n> 59.5 \n> 63  \nSNR (dB) \n46 \n46 \n52 \n \n \n \n8  •  Falcon4-CLHS Specifications \nFalcon™ 4-CLHS Series \nQuantum Efficiency Curves M2240, M4400, M4480 The response curves describe the sensor, excluding lens and light source characteristics. Spectral Responsivity \n \n \nEffective Quantum Efficiency \n \n \n \nFalcon™ 4-CLHS Series \nFalcon4-CLHS Specifications  •  9 \nFalcon4-CLHS Specifications: M6200, M8200 \n \nSupported Features \nM6200 \nM8200 \nResolution \n6144 x 6144 \n8192 x 8192 \nSensor \nE2V Emerald 37M \nE2V Emerald 67M \nPixel Size \n2.5 µm x 2.5 µm \n2.5 µm x 2.5 µm \nShutter Type  \nFull frame electronic global shutter function \nFull frame electronic global shutter function \nFull Well Charge  \n> 4 ke– \n> 4 ke– \nMaximum Frame Rate (8-bit) \n120 fps \n90 fps \nCLHS configuration (X-Protocol) \n7-Lanes \n7-Lanes \nPixel Format (Mono) \n \nMonochrome 8-bit or 10-bit \nMonochrome 8-bit or 10-bit \nSensor Synchronization \nSynchronous mode via external trigger signal or \nfree running \nSynchronous mode via external trigger signal or \nfree running \nTrigger to Exposure Minimum \nDelay (Synchronous Exposure) \n8 µs (8-bit) \n8 µs (8-bit) Trigger to Exposure Minimum \nDelay (Reset Exposure) \n8 µs (8-bit) \n8 µs (8-bit) Trigger to Exposure Start Jitter \n(Synchronous Exposure) \nUp to 1 line time \nUp to 1 line time \nTrigger to Exposure Start Jitter  \n(Reset Exposure) \n0"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 30, "content": "µs \n0 µs \nExposure Time Minimum \n(see exposureTimeActual) \n8 µs \n8 µs \nHorizontal Line Time \n2.68 µs \n2.68 µs \nMin. Time from End of Exposure \nto Start of Next Exposure  \n7.5 µs \n7.5 µs \nReadout Time (full frame size) Number of rows must be a multiple of 2 \n8 243 µs (8-bit) \n14 693 µs (10-bit) \nNumber of rows must be a multiple of 2 \n10 980 µs (8-bit) 19 573 µs (10-bit) \nBlack Offset Control \nYes (in DN) Yes (in DN) \nGain Control \nIn-sensor Analog Gain (1x to 4x),  \nFPGA Digital Gain \nIn-sensor Analog Gain (1x to 4x),  \nFPGA Digital Gain \nDefective Pixel Replacement \nYes, up to 6132 pixel positions Yes, up to 6132 pixel positions \nImage Correction \nFlat Field Correction (Factory + 1 user set)  \nLens Shading Correction  (Factory + 1 user set) Flat Field Correction (Factory + 1 user set)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 31, "content": "Lens Shading Correction  (Factory + 1 user set) \nMulti-ROI Support \nFuture \nFuture \nOutput Dynamic Range (dB)  \n> 56 \n> 56 \nSNR (dB) \n36 \n36 \n \n \n \n \n10  •  Falcon4-CLHS Specifications \nFalcon™ 4-CLHS Series \nQuantum Efficiency Curves M6200, M8200 \nThe response curves describe the sensor, excluding lens and light source characteristics. Spectral Responsivity \n  \nEffective Quantum Efficiency \n \n \n \n \nFalcon™ 4-CLHS Series \nInstallation  •  11 \nInstallation If you are familiar with CLHS cameras and Teledyne DALSA frame grabbers follow the Quick Start section to \nquickly install and acquire images with the Falcon4-CLHS and the CamExpert tool provided with Sapera LT. If using CLHS cameras with frame grabbers is new to you, review the Requirements section for what you need \nbefore you start. See Installation Details for additional information on Sapera LT installation. Refer to your frame \ngrabber's user manual for instructions related to the board installation."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 32, "content": "Note that you need administrator rights for installation and updates. Requirements \nFrame Grabber and Cables \nA frame grabber board such as the Teledyne DALSA Xtium2-CLHS PX8 / PX8 LC is the recommended computer \ninterface. Falcon4 Model \nTeledyne DALSA Frame Grabber \nPart Number \nM2240 \nM4400 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \nXtium2 CLHS PX8 LC \nOR-A8S0-PX840 \nM4480 \nM6200 \nM8200 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \n \nCamera Link HS Cables  \nThe camera uses a Camera Link HS SFF-8470 (CX4) cable; AOC (Active Optical Connectors) cables are \nrecommended due to the high-bandwidth CLHS X-Protocol (C3 copper cables < 2m may work but are not \nrecommended). See Falcon4-CLHS Connectors and Status LED. NOTE \nCX4 AOC cables are directional; ensure that the connector labelled Camera and FG are attached accordingly \nto the camera and frame grabber. Visit our web site for additional information on the CLHS interface: \nwww.teledynedalsa.com/en/learn/knowledge-center/clhs/  \nCamera Power  \nCameras with part number FA-HMxx-xxxxx support Power via the Auxiliary Connector (12 to 24 Volt DC)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 33, "content": "See \nFalcon4-CLHS Connectors and Status LED. 12  •  Installation \nFalcon™ 4-CLHS Series \nNOTE The frame grabber PoCL (Power-over-Cable) powers the electronics in the Active Optical Cable (AOC) module. This frame grabber feature should not be disabled for normal operation. Software, firmware, and device driver downloads \nDownload the appropriate camera firmware, software and board driver from the Teledyne DALSA website. If the \nrequired version is not available, contact your Teledyne DALSA representative."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 34, "content": "FALCON4-\n<PERSON>L<PERSON> Model \nFalcon4-CLHS Firmware Design \nSoftware SDK \nXtium2-CLHS \nPX8/PX8 LC \nBoard Driver \nM2240 \nM4400 \nFalcon4-CLHS_e2v_11M_STD_Firmware_256.293.cbf  \nor higher \nSapera LT 8.6 (or \nhigher) Version 1.31 \nor higher \nM4480 \n \nFalcon4-CLHS_e2v_11M_STD_Firmware_256.101.cbf  \nor higher \nSapera LT 8.6 (or \nhigher) Version 1.31 \nor higher \nM6200 \nM8200 \nFalcon4-CLHS_e2v_67M_STD_Firmware_254.124.cbf  \nor higher \nSapera LT 8.70 \n(or higher) Version 1.40 \nor higher \n \nThe latest Falcon4-CLHS firmware files can be downloaded from the Teledyne DALSA website: \nwww.teledynedalsa.com/en/support/downloads-center/firmware \n \nSapera LT SDK (full version) is the image acquisition and control software development kit (SDK) for Teledyne \nDALSA cameras. It includes the CamExpert application, which provides a graphical user interface to access \ncamera features for configuration and setup. Sapera LT is available for download from the Teledyne DALSA \nwebsite: \nwww.teledynedalsa.com/en/products/imaging/vision-software/sapera-lt/download/ \n \nXtium2-CLHS PX8/PX8 LC Board Drivers are available from the Teledyne DALSA website."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 35, "content": "Follow the \ninstallation instructions from the board’s User Manual for the computer requirements, installation, and update. www.teledynedalsa.com/en/support/downloads-center/device-drivers/  \n \n \n \nFalcon™ 4-CLHS Series \nInstallation  •  13 \nQuick Start (using a Teledyne DALSA Frame \nGrabber) The following steps summarize the installation procedure. You need administrator rights for installation and \nupdates. WARNING – GROUNDING INSTRUCTIONS \nStatic electricity can damage electronic components. It’s critical that you discharge any static electrical charge \nby touching a grounded surface, such as the metal computer chassis, before handling the frame grabber."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 36, "content": "Before installing the frame grabber, power off the computer and disconnect the power cord. • \nTurn off computer and disconnect power cord. • \nInstall the Xtium2-CLHS PX8 (or PX8 LC) into an available PCI Express x8 Gen3 slot. Follow instructions \nfrom the frame grabber's user manual. • \nReconnect power cord and turn on the computer. • \nDownload and install the Sapera LT SDK or its runtime library: \n• \nversion 8.6 or newer required for models M2240, M4400, M4480 \n• \nversion 8.7 or newer required for models M6200, M8200 \n• \nDownload and install the Xtium2-CLHS PX8/PX8 LC board driver: \n• \nversion 1.31 or newer required for models M2240, M4400, M4480 \n• \nversion 1.40 or newer required for models M6200, M8200 \n• \nUpdate the board firmware, if required (a dialog will open in that case)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 37, "content": "• \nReboot the computer. • \nConnect the Falcon4-CLHS with a CLHS camera cable to the CLHS frame grabber. • \nPower the camera using an appropriate power supply. The Falcon4-CLHS status LED will indicate power and \nthe Device / Host connection with a steady green color when connected. See section Camera Status LED \nIndicator for a complete list of Status LED indicators. Once installed, upload new camera firmware and test acquisition: \n• \nStart CamExpert. The plug-and-play feature of the frame grabber and camera will automatically configure \nframe buffer, data lanes, and frame rate parameters to match the Falcon4 model being used. At this time do \nnot configure for an external trigger. • \nUpload new camera firmware. See Updating Firmware via File Access in CamExpert. • \nFrom the Falcon4 Image Format Feature Category, select a test pattern from the Test Image Selector \nParameter. • \nClick Grab."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 38, "content": "You will see the pattern in the CamExpert display window. • \nIf a camera lens is attached, turn off the test pattern and grab live again. Adjust the lens aperture plus focus, \nand/or adjust the camera Exposure Time and Frame Rate as required. 14  •  Installation \nFalcon™ 4-CLHS Series \nInstallation Details \nSapera LT Installation \nStart the Sapera LT installer and follow instructions. On the Acquisition Components page, select the Teledyne \nDALSA frame grabbers and CameraLink GenCP compatible cameras option. The installation program will prompt to reboot the computer."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 39, "content": "It is not necessary to reboot the computer between \nthe installation of Sapera LT and the installation of the board driver. Reboot will be required after software and \nboard driver are installed. Board Driver Installation \nFollow instructions in the frame grabber's user manual for installation of the frame grabber and board driver. Falcon™ 4-CLHS Series \nInstallation  •  15 \nTesting Acquisition \nStart CamExpert \nSapera CamExpert is included as part of the Sapera LT SDK. It is Teledyne DALSA’s camera and frame grabber \ninterfacing tool that allows you to quickly validate hardware setup, change parameter settings, and test image \nacquisition. It is available from the Windows Start menu under Teledyne DALSA Sapera LT, or from the desktop \nshortcut (created at installation)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 40, "content": "If there is only one Teledyne DALSA frame grabber, the Device list automatically has the Xtium2-CLHS PX8 \nselected and the connected Falcon4-CLHS is also automatically detected as shown in the image below. If the camera is not automatically detected, verify that the camera is properly powered and that the fiber optic \ncable is connected correctly to the appropriate connectors on the frame grabber and camera; cables are uni-\ndirectional and connectors are labelled Camera and F G (frame grabber). See also Using CamExpert with Falcon4-CLHS. Upload Camera Firmware \nUnder Windows, the user can upload new firmware using the Upload/Download File feature in the File Access \nControl category provided by the Sapera CamExpert tool. See Updating Firmware via File Access in CamExpert. Verify Basic Acquisition \nTo verify basic acquisition, the camera can output a test pattern to validate that parameter settings are correctly \nconfigured between the camera and frame grabber."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 41, "content": "• \nIn the Image Format category, select Test Pattern – Grey Diagonal Ramp Moving. 16  •  Installation \nFalcon™ 4-CLHS Series \n \n• \n(For models M6200 and M8200 only) In the Basic Timing board category, click the Camera Sensor Geometry \nSetting value, and select 1X-2YE Two Channel Converge as depicted. • \nOn the Display toolbar, click Fit to Screen to view the complete acquisition in the display window (the actual \nacquisition data is unmodified). • Click Grab to view the diagonal ramp acquisition."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 42, "content": "Falcon™ 4-CLHS Series \nInstallation  •  17 \n \n \n \n18  •  Installation \nFalcon™ 4-CLHS Series \nFalcon4-CLHS Connectors and Status LED \nConnectors \nThe Falcon4-CLHS has connectors for CX4 data/control and I/O:  \n• \nA 10 pin I/O (Samtec) connector for camera power, trigger, strobe and general I/O signals. The connector \nsupports a retention latch, while additionally the case supports an I/O cable with locking thumbscrews. Teledyne DALSA provides optional cables for purchase (see I/O Cable Accessories). Also see 10-pin I/O \nConnector Details for pin out specifications. • \nA CX4 connector supporting the CLHS data output and control signals."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 43, "content": "See Cable Manufacturers Contact \nInformation for a variety of CX4 cables. The following figure of the Falcon4-CLHS back shows connector and LED locations along with identification \nlabels. See Mechanical Specifications for details on the connectors and camera mounting dimensions. Falcon4-CLHS – Rear View \nLED Indicators \nThe Falcon4-CLHS has one multicolor LED to provide a simple visible indication of camera state, as described \nbelow. The CX4 connector does not have any status LED indicator. Camera Status LED Indicator \nThe camera is equipped with one LED to display its operational status."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 44, "content": "When more than one condition is active, \nthe LED color indicates the condition with the highest priority. The following table summarizes the LED states. Falcon™ 4-CLHS Series \nInstallation  •  19 \nLED State \nDefinition \nLED is off \nNo power to the camera \nFlashing Orange \nCamera initialization sequence in progress. Flashing Green \nLooking for link; hardware is fine but connection not established. Steady Green  \nLink established. Device and host connected and data transfer may take place."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 45, "content": "Fast Flashing Blue \nFile Access Feature is transferring data such as a firmware update, etc. \n \n \nConstant Red \nSystem error (for example, internal error). Preventing Operational Faults due to ESD \n \nWARNING \nCamera installations which do not protect against ESD (electrostatic discharge) may exhibit operational faults. Problems such as random data loss, random camera resets and other non-reoccurring control issues may all \nbe solved by proper ESD management. Teledyne DALSA has performed ESD testing on cameras using an 8 kilovolt ESD generator without any \nindication of operational faults. To help prevent ESD problems, mount the camera on a metallic platform with a good connection to earth ground. See also Sensor Handling Instructions."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 46, "content": "20  •  Operational Reference \nFalcon™ 4-CLHS Series \nOperational Reference \nUsing CamExpert with Falcon4-CLHS \nThe Sapera CamExpert tool allows a user to test the camera and frame grabber combination and their functions. CamExpert saves the Teledyne DALSA frame grabber user settings as individual camera parameter files on the \nhost system (*.ccf). The camera settings are saved within the camera as a user set. An important component of CamExpert is its live acquisition display window which allows immediate verification of \ntiming or control parameters without the need to run a separate acquisition program. CamExpert Panes \nThe various areas of CamExpert are summarized in the figure below. Device Selector\nFeature \nCategories\nAcquisition \nDisplay\nMessage \nWindow\nCamExpert \nControl Buttons\nFeature Values \nQuick Guide\nFeature List\nIn Black: user can change\nIn Gray: read-only status\nFeatures may become active \ndependent on other feature settings."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 47, "content": "Acquisition \nInformation\n \n• \nDevice Selector pane: View and select from any installed Sapera acquisition device if more than one is \ninstalled in the computer. After a device is selected CamExpert will only present parameters applicable to that \ndevice. Falcon™ 4-CLHS Series \nOperational Reference  •  21 \n• \nParameters pane: Allows viewing or changing all acquisition parameters supported by the acquisition device \nor frame grabber. This avoids confusion by eliminating parameter choices when they do not apply to the \nhardware in use. When using a Teledyne DALSA frame grabber and camera, CamExpert groups all frame grabber parameters \nunder the Board heading, and the supported camera features under the Attached Camera heading."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 48, "content": "• \nDisplay pane: Provides a live or single frame acquisition display. Frame buffer parameters are shown in an \ninformation bar above the image window. • \nControl Buttons: The Display pane includes CamExpert control buttons. These are: \n \n \n \nAcquisition control button: Click once to start the frame grabber live grab mode, click again to stop. The Falcon4 is always in free running acquisition mode unless configured to use an external \ntrigger."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 49, "content": "Single frame grab: \nClick to acquire one frame from the frame grabber device. Software trigger button: With the I/O control parameters set to Trigger Enabled / Software Trigger type, click to send a \nsingle software trigger command. CamExpert display controls: \n(these do not modify the frame buffer data) \nStretch (or shrink) image to fit, set image display to original size, or zoom the image to any \nsize and ratio. Note that under certain combinations of image resolution, acquisition frame \nrate, and host computer speed, the CamExpert screen display may not update completely due \nto the host CPU running at near 100%. This does not affect the acquisition."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 50, "content": "Histogram / Profile tool: \nSelect to view a histogram or line/column profile during live acquisition. • \nOutput Messages pane: Displays messages from CamExpert, camera or the interface driver. • \nLink Signals: Displays the status of various Link. CamExpert View Parameters Option While the Board section shows all frame grabber parameters, the Attached Camera section shows camera \nfeatures filtered by a Visibility attribute that selects the targeted user level. These vary from Beginner (features \nrequired for basic operation of the device) to Guru (optional features required only for complex operations)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 51, "content": "Choose the parameter visibility via the [ << Less  More>> ] control below each feature list. You can also choose \nthe visibility level from the View > Parameters Options > Visibility menu. 22  •  Operational Reference \nFalcon™ 4-CLHS Series \nCamera Feature Categories The following sections describe the available categories and their features in detail. Many of the features shown in CamExpert may be changed directly in CamExpert or programmatically via an \nimaging application. Their availability may depend on other feature settings, and while some features are read \nonly, others may be changed during acquisition."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 52, "content": "Note that features shown by CamExpert may change with \ndifferent Falcon4 models implementing different sensors and image resolutions; that is, a specific camera model \nmay not support the full feature set defined in a category. The tables found in each category describe the features and their possible values, along with their view attribute \n(beginner, expert, guru) and the device version in which the feature was introduced. A device version number \nrepresents the camera software functional group, not a firmware revision number. As Falcon4 capabilities evolve, \nthe device version increases, identifying the supported function package. New features for a major device version \nrelease are indicated by green text for easy identification. For each feature, the device version may differ for each \ncamera sensor available."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 53, "content": "The last column also indicates whether the parameter is a member of the DALSA Features Naming Convention \n(DFNC), or of the GenICam Standard Features Naming Convention (SFNC–tag not shown). Features tagged as \nInvisible are usually for Teledyne DALSA or third-party software usage—not typically needed by end user \napplications. Falcon™ 4-CLHS Series \nOperational Reference  •  23 \nCamera Information Category  \nCamera information can be retrieved via a controlling application. Parameters such as camera model, firmware \nversion, etc., uniquely identify the connected Falcon4-CLHS device and provide information on its state. These \nfeatures are typically read-only. Camera Information Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nManufacturer Name \nDeviceVendorName \nDisplays the device vendor name."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 54, "content": "1.00 \nBeginner \nDevice Family Name \nDeviceFamilyName \nDisplays the device family name. 1.00 \nBeginner \nModel Name \nDeviceModelName \nDisplays the device model name. 1.00 \nBeginner \nDevice Version \nDeviceVersion \nDisplays the device version. This tag will also \nhighlight if the firmware is a beta or custom \ndesign. 1.00 \nBeginner \nManufacturer Part \nNumber \ndeviceManufacturerPartNumber \nDisplays the device manufacturer part number. 1.00 \nDFNC \nBeginner \n \n \n24  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nManufacturer Info \nDeviceManufacturerInfo"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 55, "content": "This feature provides extended manufacturer \ninformation about the device. Falcon4 cameras \nshow which firmware design is currently loaded. 1.00 \nBeginner \nFirmware Version \nDeviceFirmwareVersion \nDisplays the currently loaded firmware version \nnumber. Firmware files have a unique number \nand have the .cbf file extension. 1.00 \nBeginner \nSerial Number  \nDeviceSerialNumber \nSerial number of the camera. 1.00 \nBeginner \nDevice User ID \nDeviceUserID \nThis feature stores a user-programmable \nidentifier. 1.00 \nBeginner \nTemperature \nDeviceTemperature \nInternal temperature in degrees Celsius. 1.00 \nBeginner \nInput Voltage \ndeviceInputVoltage \nVoltage at power connector (V). 1.00 \nDFNC \nBeginner \nDevice Built-In Self \nTest Status \ndeviceBISTStatus \nList the BIST status. Display the most critical error \nif there are multiple errors. 1.00 \nBeginner \n \nPassed \nNo errors. I2C \nErrors on I2C devices."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 56, "content": "SENSOR_CAL \nSensor calibration failed. SENSOR_SPI \nSensor SPI failed self test. FPGA_ECHO_BACK \nFPGA failed echo back. FLASH_TIMEOUT \nSPI flash timeout. FLASH_ERROR SPI flash ID invalid (communication fail). NO_FPGA_CODE \nFPGA: no code in SPI flash. NO_COMMON_SETTINGS \nNo common settings. NO_FACTORY_SETTINGS \nNo factory settings. OVER_TEMPERATURE \nOver temperature. SENSOR_PATTERN \nSensor Test pattern failed. CLHS_TXRDY_RETRY \nCLHS Tx Retry. INVALID_UPGRADE \nFirmware upgrade failure. NO_USER_SETTINGS \nNo user settings. NO_SCRIPT \nNo start script. \n \n \nFACT_CODE \nRun from factory code. NO_FATFS \nNo FAT filesystem. WRONG_DAC \nWrong DAC values. Device Built-In Self \nTest Status All \ndeviceBISTStatusAll \nResult of Basic Internal Self-Test \n1.00 \nDFNC \nBeginner \nRefresh BIST \ndeviceBIST \nRefresh Basic Internal Self-Test \n1.00 \nBeginner \nDevice Reset \nDeviceReset \nWrite to this feature to reset the device to its \npower up state."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 57, "content": "1.00 \nBeginner \nPower-on User Set \nUserSetDefaultSelector \nSelects the feature User Set to load at camera \nreset. 1.00 \nBeginner \nFactory Setting \nDefault \nSelect the default camera feature settings saved \nby the Factory. User Set 1 \nUserSet1 \nSelect the user defined configuration UserSet 1 as \nthe Power-up Configuration. User Set 2 \nUserSet2 \nSelect the user defined configuration UserSet 2 as \nthe Power-up Configuration. Falcon™ 4-CLHS Series \nOperational Reference  •  25 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nUser Set Selector \nUserSetSelector \nSelects the camera configuration set to load \nfeature settings from or save current feature \nsettings to. The Factory set contains default \ncamera feature settings."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 58, "content": "1.00 \nBeginner \nFactory Setting \nDefault \nSelect the default camera feature settings saved \nby the factory. UserSet 1 \nUserSet1 \nSelect the User Defined Configuration space \nUserSet1 to save to or load from features settings \npreviously saved by the user. UserSet 2 \nUserSet2 \nSelect the User Defined Configuration space \nUserSet2 to save to or load from features settings \npreviously saved by the user. Load User Set \nUserSetLoad \nLoads the camera configuration set specified by \nthe User Set Selector feature, to the camera and \nmakes it active. 1.00 \nBeginner \nSave User Set \nUserSetSave \nSaves the current camera configuration to the \nuser set specified by the User Set Selector \nfeature. 1.00 \nBeginner \n \n \n \n \nSerial Number \nDeviceID \nDisplays the device’s factory set camera serial \nnumber."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 59, "content": "1.00 \nInvisible \nDevice TL Version \nMajor \nDeviceTLVersionMajor \nMajor version of the device’s Transport Layer. 1.00 \nInvisible \nDevice TL Version \nMinor \nDeviceTLVersionMinor \nMinor version of the device’s Transport Layer. 1.00 \nInvisible \nTemperature Monitor \ntemperatureMonitorON \nTurn on/off the temperature monitor function. 1.00 \nDFNC \nInvisible \nDFNC Major Rev \ndeviceDFNCVersionMajor \nMajor revision of Dalsa Feature Naming \nConvention which was used to create the device’s \nXML. 1.00 \nDFNC \nInvisible \nDFNC Minor Rev \ndeviceDFNCVersionMinor \nMinor revision of Dalsa Feature Naming \nConvention which was used to create the device’s \nXML. 1.00 \nDFNC \nInvisible \nSFNC Major Rev \nDeviceSFNCVersionMajor \nMajor Version of the Genicam Standard Features \nNaming Convention which was used to create the \ndevice’s XML."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 60, "content": "1.00 \nDFNC \nInvisible \nSFNC Minor Rev \nDeviceSFNCVersionMinor \nMinor Version of the Genicam Standard Features \nNaming Convention which was used to create the \ndevice’s XML. 1.00 \nDFNC \nInvisible \nSFNC SubMinor Rev \nDeviceSFNCVersionSubMinor \nSubMinor Version of the Genicam Standard \nFeatures Naming Convention which was used to \ncreate the device’s XML. 1.00 \nInvisible \n \n \n \n26  •  Operational Reference \nFalcon™ 4-CLHS Series \nPower-up Configuration Dialog \nCamExpert provides a dialog box which combines the features to select the camera power-up state and to save \nor load a Falcon4 camera state. Camera Power-up Configuration \nThe Camera Power-up Configuration list allows the selection of the camera configuration state to load on \npower-up (see feature UserSetDefaultSelector). The user chooses from one factory data set or one of two \npossible user saved states. Load / Save Configuration \nThe Load/Save Configuration list allows the user to change the camera configuration any time after a power-up \n(see feature UserSetSelector)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 61, "content": "• To reset the camera to the factory configuration, select Factory Setting and click Load. • To save a current camera configuration, select a user set and click Save.  \n• \nTo restore a saved configuration, select a saved user set and click Load. Falcon™ 4-CLHS Series \nOperational Reference  •  27 \nCamera Control Category \nThe Falcon4-CLHS Camera Control category, as shown by CamExpert, groups sensor specific parameters, which \nincludes controls for frame rate, exposure time, gain, etc. Camera Control Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nDevice Scan Type \nDeviceScanType \nScan type of the sensor."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 62, "content": "1.00 \nBeginner \nAreascan \nAreascan \n2D Area-scan sensor \n \nSensor Color Type \nsensorColorType \nSensor color type. 1.00 \nDFNC \nBeginner \nMonochrome \nMonochrome \nMonochrome. Input Pixel Size \npixelSizeInput \nSize of the image input pixels, in bits per pixel. 1.00 \nDFNC \nGuru \n10 Bits/Pixel \nBpp10 \nSensor output data path is 10 bits per pixel. Sensor Width \nSensorWidth \nDefines the sensor width in active pixels. 1.00 \nExpert \n \n \n28  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nSensor Height \nSensorHeight \nDefines the sensor height in active lines."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 63, "content": "1.00 \nExpert \nAcquisition Frame Rate \nAcquisitionFrameRate \nSpecifies the camera internal frame rate. 1.00 \nBeginner \nExposure Mode \nExposureMode \nSelects the exposure control operating mode. 1.00 \nBeginner \nTimed \nTimed \nThe length of the exposure is controlled with the \"Exposure \nTime\" feature. The exposure starts with the trigger event. Trigger Width \nTriggerWidth \nUses the width of the trigger signal pulse to control the \nexposure duration. Use the Trigger Activation feature to \nset the polarity of the trigger."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 64, "content": "The Trigger Width setting is \napplicable with Trigger Selector = Single Frame \nTrigger(Start). Long Exposure Mode \nlongExposureMode \nSelects the sensor's exposure time mode. 1.02 \nBeginner \nOff \nOff \nFor exposure times up to 50 000 µs. \nActive \nActive \nFor exposure times above 10 000 µs.  \nExposure Alignment \nexposureAlignment \nSpecifies how the exposure is executed in relationship to \nthe sensor capabilities and current frame trigger. 1.00 \nDFNC \nGuru \nSynchronous \nSynchronous \nExposure is synchronous to the internal timing of the \nsensor. The readout is concurrent to the exposure for the \nfastest possible frame rate. When a valid trigger is \nreceived and the ExposureTime is shorter than the readout \nperiod, the ExposureStart event is latched in the previous \nframe’s readout."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 65, "content": "That is, the ExposureStartEvent is \ndelayed and is initiated when the actual exposure starts \nsuch that the exposure ends and readout begins as soon \nas the previous readout has completed. Reset \nReset \nSensor timing is reset to initiate exposure when a valid \ntrigger is received. Readout is sequential to exposure, \nreducing the maximum achievable frame rates. A trigger \nreceived during exposure or readout is ignored since data \nwould be lost by performing a reset. Exposure Delay \nexposureDelay \nSpecifies the delay in microseconds to apply after the \nFrameStart event before starting the ExposureStart event. 1.00 \nGuru \nDFNC \nExposure Time \nExposureTime \nSets the exposure time of the sensor in microseconds (µs)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 66, "content": "1.00 \nBeginner \nExposure Time Actual \nexposureTimeActual \nMeasured Exposure Time in microseconds (µs). 1.00 \nDFNC \nGuru \nShutter Mode \nSensorShutterMode \nSelect the sensor shutter mode. 1.00 \nDFNC \nGuru \nGlobal \nGlobal \nSelect sensor global shutter mode. Gain Selector \nGainSelector \nSelects which gain and offset to control. 1.00 \nBeginner \nSystem Gain \nSystem \nGain applied after analog gains. Analog \nAnalogAll \nAnalog Gain and offset applied to the sensor. Digital \nDigitalAll \nApply a digital gain adjustment to the entire image. This \nindependent gain factor is applied to the image after the \nsensor. 1.02 \nGain \nGain \nVideo signal multiplier. 1.00 \nBeginner \nBlack Level Selector \nBlackLevelSelector \nSelects which offset to control. 1.00 \nBeginner \nBlack Level \nDigitalAll \nOffset applied to the whole image. Analog \nAnalogAll \nAnalog Gain and offset applied to the sensor."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 67, "content": "Falcon™ 4-CLHS Series \nOperational Reference  •  29 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nBlack Level \nBlackLevel \nA signed offset added to the output. DN_out = (DN_in + Black_Level) * Gain \n1.00 \nBeginner \nFast Readout Mode \nfastReadoutMode \nSelects the sensor’s readout mode. 1.00 \nDFNC \nGuru \nOff \nOff \nWhen this mode is off, the sensor is operated in low noise \nmode; row timing and/or row readout are normal. Active \nActive When this mode is active, the sensor is operated in high \nspeed mode; row timing and/or row readout are shorter."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 68, "content": "Long Exposure Mode, Time Exposure, Fast Readout Mode, and Gain \nThe setting of the Long Exposure Mode feature affects other feature settings, as described in the table below. Model \nLong Exposure Mode \nExposure Time Range (µs) \nAnalog Gain \nFast Readout Mode \nAll \nOff \n5 – 50 000 \n1x to 4x \nOff/Active \nM2240, M4400, M4480 \nActive \n10 000 – 500 000 \nMinimum 1.5x \nOff \nM6200, M8200 \nActive \n10 000 – 500 000 \n1x to 4x \nN/A* \n* Fast Readout Mode is not available on the M6200 or M8200. Note that Fast Readout and Long Exposure cannot be both active; setting Long Exposure Mode to Active will \nautomatically set Fast Readout Mode to Off. The following diagram depicts the effect of Long Exposure Mode and Fast Readout Mode on the frame rate and \nexposure (integration) time."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 69, "content": "(Provided as an example, does not reflect the specific exposure time range of this \nmodel.) \n \n \n \n30  •  Operational Reference \nFalcon™ 4-CLHS Series \nDigital IO Control Category \nThe Digital IO Control category, as shown by CamExpert, groups features used to configure acquisition inputs \nand outputs. Digital IO Control Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nTrigger Selector \nTriggerSelector \nSelects which type of trigger to configure with the various \nTrigger features. 1.00 \nGuru \nSingle Frame Trigger(Start) FrameStart \nSelects a trigger starting the capture of a single frame. MultiFrame Trigger(Start) \nFrameBurstStart \nSelects a trigger to capture multiple frames. The number of \nframes is specified by the “triggerFrameCount” feature."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 70, "content": "Trigger Mode \nTriggerMode \nControls whether the external trigger is active. 1.00 \nBeginner \nOff \nOff \nLine rate is controlled by Acquisition Frame Rate feature. On \nOn \nTrigger comes from CLHS (frame grabber) or GPIO. Falcon™ 4-CLHS Series \nOperational Reference  •  31 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nTrigger Frames Count \ntriggerFrameCount \nSets the total number of frames to acquire when a valid \ntrigger is received. This feature is available when Trigger \nSelector = MultiFrame Trigger(Start). 1.00 \nGuru \nDFNC \n \nTrigger Source \nTriggerSource \nSpecifies the source of the external trigger."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 71, "content": "TriggerMode \nmust be set to On. 1.00 \nGuru \nLine 1 \nLine1 \nSelect Line1 (and associated I/O control block) to use as \nthe external trigger source. Line 2 \nLine2 \nSelect Line2 (and associated I/O control block) to use as \nthe external trigger source. CLHS In \nLinkTrigger0 \nTrigger comes from frame grabber over LinkTrigger0. Timer1 End \nTimer1End \nSelect the Timer End event as the internal trigger source. Counter1 End \nCounter1End \nSelect the Counter End event as the internal trigger source."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 72, "content": "Software \nSoftware \nThe trigger command source is only generated by software \nusing the TriggerSoftware command. Software Trigger \nTriggerSoftware \nGenerates an internal trigger. The TriggerSource feature \nmust be set to Software. 1.00 \nGuru \nTrigger Input Line Activation \nTriggerActivation \nEdge of the input signal that will trigger camera. 1.00 \nGuru \nRising Edge \nRisingEdge \nSpecifies that the trigger is considered valid on the rising \nedge of the source signal. Falling Edge \nFallingEdge \nSpecifies that the trigger is considered valid on the falling \nedge of the source signal."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 73, "content": "Any Edge \nAnyEdge \nSpecifies that the trigger is considered valid on the falling or \nrising edge of the source signal. Trigger Delay \nTriggerDelay \nSpecifies the delay in microseconds to apply after receiving \nthe trigger and before activating the trigger event. 1.00 \nGuru \nTrigger Overlap \nTriggerOverlap \nStates if a trigger overlap is permitted with the Active Frame \nreadout signal. This feature defines if a new valid trigger will \nbe accepted (or latched) for a new frame. 1.00 \nGuru \nOff \nOff \nNo trigger overlap is permitted. ReadOut \nReadOut \nTrigger is accepted immediately after the start of the \nreadout."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 74, "content": "Line Selector \nLineSelector \nSelects the physical line (or pin) of the external device \nconnector to configure. 1.00 \nBeginner \nLine 1 \nLine1 Selects Line 1 (input 1) Line 2 \nLine2 \nSelects Line 2 (input 2) \n \nLine 3 \nLine3 \nSelects Line 3 (output 1) \n \nLine 4 \nLine4 \nSelects Line 4 (output 2) \n \nLine 5 \nLine5 \nSelects Line 5 (output 3) \n \nLine 6 \nLine6 Selects Line 6 (output 4) \n \nLine Name \nlineName \nDescription of the physical Pin associated with the logical \nline. 1.00 \nBeginner \nDFNC \nInput 1 \nInput1 \nAssociated with the logical line Input 1 \nInput 2 \nInput2 \nAssociated with the logical line Input 2 \n \nOutput 1 \nOutput1 \nAssociated with the logical line Output 1 \n \nOutput 2 \nOutput2 \nAssociated with the logical line Output 2 \n \nOutput 3 \nOutput3 \nAssociated with the logical line Output 3 \n \nOutput 4 \nOutput4 \nAssociated with the logical line Output 4 \n \nLine Format \nLineFormat \nSpecify the current electrical format of the selected physical \ninput or output."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 75, "content": "1.00 \nBeginner \nOpto-Coupled \nOptoCoupled The line is opto-coupled. 32  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nLine Mode \nLineMode \nReports if the physical Line is an Input or Output signal. 1.00 \nBeginner \nInput \nInput The line is an input line. Output \nOutput"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 76, "content": "The line is an output line. Line Status \nLineStatus \nReturns the current status of the selected input line. 1.00 \nExpert \nLow \nLow \nLine level is low \nHigh \nHigh \nLine level is high \nLine Status All \nLineStatusAll \nReturns the current status of all available line signals, at \ntime of polling, in a single bitfield. The order is Line1, \nLine2... 1.01 \nExpert \nLine Inverter \nLineInverter \nControls whether to invert the polarity of the selected input \nor output line signal. 1.00 \nBeginner \nOff \nOff \nLeave signal unchanged \nOn \nOn \nInvert line signal \nInput Line Detection Level \nlineDetectionLevel \nSpecifies the voltage threshold required to recognize a \nsignal transition on an input line."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 77, "content": "1.00 \nBeginner \nDFNC \nThreshold for TTL \nThreshold_for_TTL \nA signal below 0.8V will be detected as a Logical LOW and \na signal greater than 2.4V will be detected as a Logical \nHIGH on the selected input line. Input Line Debouncing Period \nlineDebouncingPeriod \nSpecifies the minimum delay (us) before an input line \nvoltage transition is recognized as a signal transition. 1.00 \nBeginner \nDFNC \nOutput Line Source \noutputLineSource \nSelects which internal signal, event driven pulse or software \ncontrol state to output on the selected Line. LineMode must \nbe Output. 1.00 \nBeginner \nDFNC \nOff \nOff \nLine output is disabled (Tri-State) or Open with \nOptocoupled output. Software Controlled \nSoftwareControlled \nThe OutputLineValue feature changes the state of the \noutput."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 78, "content": "Pulse On: Start of Frame \nPulseOnStartofFrame \nGenerate a pulse on the start of the Frame Active event. Pulse On: Start to Exposure \nPulseOnStartofExposure \nGenerate a pulse on the ExposureStart event. Pulse On: End of Exposure \nPulseOnEndofExposure \nGenerate a pulse on the End of Exposure event. Pulse On: Start Of Readout \nPulseOnStartOfReadout \nGenerate a pulse on the ReadoutStart event. Pulse On: End Of Readout \nPulseOnEndOfReadout \nGenerate a pulse on the ReadoutEnd event. Pulse On: Valid Frame Trigger \nPulseOnValidFrameTrigger \nGenerate a pulse on the FrameTrigger event."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 79, "content": "Pulse On: Invalid Frame Trigger \nPulseOnInvalidFrameTrigger \nGenerate a pulse on the Invalid Frame(s)Trigger event. Pulse On: End of Timer1 \nPulseOnEndofTimer1 \nGenerate a pulse on the end of timer1. Pulse On: End of Counter1 \nPulseOnEndofCounter1 \nGenerate a pulse on the end of counter1. Pulse On: Input1 \nPulseOnInput1 \nGenerate a pulse on the Input Signal 1 event. Pulse On: Input2 \nPulseOnInput2 \nGenerate a pulse on the Input Signal 2 event. Pulse On: Link CLHS In \nPulseOnLinkTrigger0 \nGenerate a pulse on LinkTrigger0 signal."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 80, "content": "Exposure Active \nExposureActive \nGenerate the Exposure Active state on specific output. Output Line Pulse Activation \noutputLinePulseActivation \nSpecifies the input line activation mode to trigger the \nOutputLine pulse. 1.00 \nBeginner \nDFNC \nRising Edge \nRisingEdge \nSpecifies that the trigger is considered valid on the rising \nedge of the source signal. Falling Edge \nFallingEdge \nSpecifies that the trigger is considered valid on the falling \nedge of the source signal. Any Edge \nAnyEdge \nSpecifies that the trigger is considered valid on the falling or \nrising edge of the source signal. Output Line Pulse Delay \noutputLinePulseDelay \nSets the delay (µs) before the output line pulse duration \nsignal."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 81, "content": "1.00 \nDFNC \nBeginner \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  33 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nOutput Pulse Duration \noutputLinePulseDuration \nSets the width (duration) of the output line pulse in \nmicroseconds (µs). 1.00 \nDFNC \nBeginner \nOutput Line Value \noutputLineValue \nSet the GPIO out value when outputLineSource is \nSoftwareControlled. 1.00 \nDFNC \nExpert \nActive \nActive \nSets the output circuit to close. Inactive \nInactive \nSets the output circuit to open. I/O Module Block Diagram \nPhysical\n Line\nInput \ninverter\nLineStatus\nTrigger \nLine \nActivation \nTrigger Signal\nTimer\nTimerEnd Event\nCounterEnd Event \nSoftware Trigger \nCmd \nInput\nTimer and Counter Module\nCounter\nLine \nDebouncer\nEvent Driven \nTrigger \nSource \nTrigger Module\nLine\nDetection\nLevel\n \nTrigger Mode Details \nFalcon4-CLHS image exposures are initiated by an event. The trigger event is either the camera’s programmable \ninternal clock used in free running mode, an external input to the controlling frame grabber used for synchronizing \nexposures to external triggers, or a programmed function call message by the controlling computer."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 82, "content": "These \ntriggering modes are described below. • \nFree running (Trigger Mode = Off): The free-running mode has programmable internal timers for frame rate \nand exposure period. Frame rate minimums, maximums and increments supported are sensor specific. Maximum frame rates are dependent on the required exposure. • \nTrigger Source (Trigger Mode = On): Exposures are controlled by an external trigger signal where the \nspecific input line is selected by the Trigger Source feature. Trigger Source Types (Trigger Mode = On) \n• \nTrigger Source = Line1: The frame grabber initiates the exposure via the external line 1.  \n• \nTrigger Source = Line2: The frame grabber initiates the exposure via the external line 2. \n• \nTrigger Source = CLHS"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 83, "content": "In: The external trigger comes from frame grabber over LinkTrigger0. • \nTrigger Source = Timer1End Event: The Timer1 End Event is used as the internal trigger source. Refer to \nCounter and Timer Controls for information on those features. • \nTrigger Source = Counter1End Event: The Counter1 End Event is used as the internal trigger source.  \n• \nTrigger Source = Software: An exposure trigger is sent as a software command. Software triggers cannot \nbe considered time accurate due to computer latency and sequential command jitter."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 84, "content": "But a software trigger is \nmore responsive than calling a single-frame acquisition since the latter must validate the acquisition \nparameters and modify on-board buffer allocation if the buffer size has changed since the last acquisition. 34  •  Operational Reference \nFalcon™ 4-CLHS Series \nTrigger Overlap: Feature Details \nThe Trigger Overlap feature defines how the Falcon4-CLHS handles triggers that might occur more frequently \nthan the Frame Active period (an exposure plus readout period). If TriggerOverlap = Off, then triggers received \nbefore the end of the Frame Active period are ignored. Other TriggerOverlap values are dependent on the camera \nmodel and sensor used. TriggerOverlap = Off  \nNo trigger overlap is permitted. Diagram Conditions: \n• \nTriggerMode"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 85, "content": "= On \n• \nExposureMode = Timed \n• \nTriggerActivation = RisingEdge \n• \nTriggerDelay = 0 \n• \nTriggerSelector = FrameStart \n• \nExposureAlignment = Reset \nTrigger Input\nTrigger Exclusion Period\nExposure 1\nReadout 1\nFrame 1 Active period\nTrigger Exclusion Period\nExposure 2\nReadout 2\nFrame 2 Active period\nFrame Exposure\nFrame Readout\nTriggerOverlap=Off TriggerOverlap = ReadOut \nTrigger is accepted at the beginning of the frame Readout. The “End of Exposure to Start of Readout” time is \nsensor dependent. Diagram Conditions: \n• \nTriggerMode = On \n• \nExposureMode = Timed \n• \nTriggerActivation = RisingEdge \n• \nTriggerDelay = 0 \n• \nTriggerSelector = FrameStart \n• \nExposureAlignment = Synchronous \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  35 \nTrigger Input\nTrigger Exclusion Period\nExposure 1 Readout 1\nFrame 1 Active period\nExposure 2\nReadout 2\nFrame 2 Active period\nFrame Exposure\nFrame Readout\nTriggerOverlap=Readout\nTrigger Exclusion Period\nEnd of Exposure \nto Start of Readout\nEnd of Exposure \nto Start of Readout\n \n \n \n36  •  Operational Reference \nFalcon™ 4-CLHS Series \nData Processing Category \nThe Data Processing category, as shown by CamExpert, groups features used to configure defective pixel \nreplacement and fixed pattern noise (FPN) correction."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 86, "content": "Data Processing Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nProcessing Pixel Size \nprocessingPathBpp \nPixel size in bits per pixel during processing. 1.00 \nGuru \nDFNC \n10 Bits/Pixel \nBpp10 \nSensor output data path is 10 bits per pixel. FPN Correction Mode \nFPNCorrectionMode \nSets the mode for FPN (Fixed Pattern Noise) \ncorrection. 1.00 \nExpert \nDFNC \nOff \nOff \nFPN correction is disabled. Active \nActive \nFPN correction is enabled. Calibration \nCalibration \nWhen selected, the camera is configured for \nFPN correction calibration."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 87, "content": "Some processing will \nbe disabled even if the associated feature is \nenabled. Falcon™ 4-CLHS Series \nOperational Reference  •  37 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nFPN Correction Active Set \nFPNCorrectionActiveSet \nSpecifies the current set of FPN coefficients to \nuse. This feature cannot be changed during \nacquisition. 1.00 \nExpert \nDFNC \nFactory \nFPNFactory \nSets the factory FPN coefficient table as the \ncurrent FPN set. User Set 1 \nFPNUser1 Sets User Set 1 coefficient table as the current \nFPN set."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 88, "content": "User Set 2 \nFPNUser2 \nSets User Set 2 coefficient table as the current \nFPN set. Target Exposure Range Min \nFPNCalibrationTargetExposureMin \nSets the minimum exposure time, in \nmicroseconds, for FPN calibration. 1.00 \nGuru \nDFNC \nTarget Exposure Range \nMax \nFPNCalibrationTargetExposureMax \nSets the maximum exposure time, in \nmicroseconds, for FPN calibration. 1.00 \nGuru \nDFNC \nOptical Black Reference \nFPNCalibrationOpticalBlackReference \nSets the enable state of dark current correction \nusing values generated on a row-by-row basis \nusing shielded pixels. FPN Calibration must be \nredone and saved to apply this setting. 1.00 \nGuru \nDFNC \nOff \nOff \nRow-by-row dark current correction is disabled."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 89, "content": "When the FPN Correction Mode is set to active \nonly ADC related correction is applied. Active \nActive \nRow-by-row dark current correction coefficients \nare generated during calibration and included in \nthe FPN correction for the selected user set. Note, this correction is always applied whether \nthe FPN Correction Mode is set to Active of Off. Optical Black Reference \nCalibration Offset \nFPNCalibrationOpticalBlackReferenceOffs\net \nSets the offset to apply to the optical black \nreference correction to ensure that values are \nabove 0. 1.00 \nGuru \nDFNC \nCalibration FPN \nFPNCalibrationCalibrate \nPerform a FPN correction calibration. 1.00 \nGuru \nDFNC \nSave FPN Calibration \nFPNCalibrationSave \nSave the calibration results of the FPN to the \nactive set."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 90, "content": "1.00 \nGuru \nDFNC \nDefective Pixel \nReplacement Mode \ndefectivePixelReplacementMode \nSets the mode for the defective pixel \nreplacement. 1.01 \nExpert \nDFNC \nOff \nOff \nDisable defective pixel replacement. Active \nActive \nEnable defective pixel replacement. Defective Pixel \nReplacement Map Current \nActive Set \ndefectivePixelReplacementMapCurrentAct\niveSet \nSets the defective pixel replacement map set. 1.01 \nExpert \nDFNC \nFactory Map \nFactoryMap \nFactory default defective pixel replacement map. User Set 1 \nUserMap1 \nUser defective pixel replacement map."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 91, "content": "FPN Correction \nThe fixed pattern noise (FPN) correction compensates for dark current noise unique to each camera sensor. The Falcon4 uses 2 stages of FPN correction: \n• \nRow-by-row dark current correction, using values generated with shielded reference pixels (Optical Black \nReference and Optical Black Reference Calibration Offset features). • \nADC noise correction. 38  •  Operational Reference \nFalcon™ 4-CLHS Series \nBoth stages can be enabled and disabled independently. The FPN Correction Mode and Optical Black Reference \nfeatures determine the type of FPN correction applied. When the Optical Black Reference feature is set to Active \nduring calibration, the correction coefficients are generated and always applied."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 92, "content": "Optical Black Reference \n(during calibration) FPN Correction Mode \nResult  \nActive  \nOff \nOptical Black Reference correction applied. (ADC correction disabled). Active \nOptical Black Reference and ADC correction applied. Off \nOff \nNo correction applied. Active \nADC correction applied."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 93, "content": "FPN calibration is always performed using 10-bits (regardless of the Pixel Format setting). With CMOS sensors, it is important to perform FPN calibration under the same operating conditions the camera \nwill be used, otherwise sensor variations (over temperature and exposure) will make the FPN calibration invalid. Falcon4 cameras has two FPN user memory spaces to store calibration data, allowing users to store FPN data for \ndifferent optimized exposure setups. A user set can store coefficients for specific gain settings (gain = 1, 1.5, 2.0, \n2.5, 3.0, 3.5 and 4); a calibration must be performed with each gain setting and saved to the same user set (gain \nsettings which are not user calibrated use the factory default). Performing an FPN Calibration via Sapera CamExpert  \nThe Sapera LT CamExpert tool provides an easy GUI-based method for a user to perform an FPN Calibration. For FPN calibration the camera must acquire a suitable dark image."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 94, "content": "In general, factory FPN correction is sufficient for most applications. However, if a new FPN correction is required \na user set FPN correction can be applied. NOTE \nImportant: Before calibration, the Falcon4 should be powered on long enough to achieve its nominal \ntemperature (a minimum of 30 minutes). A low ambient temperature may increase the time required for the \ncamera to reach a stable internal temperature. Important: During calibration, no other Falcon4 features should be accessed or modified. Calibration via CamExpert or via a User Application: Exposure and frame rates used during a Flat Field \nCalibration should be similar to the exposure settings used in the camera application. To perform FPN Calibration \nStep 1. Cover the lens (place the sensor in dark). Step 2. Set the FPN Correction Mode to Off and check the histogram. Using CamExpert, click <PERSON>rab and then Statistics. In the Statistics dialog, use the Selected view list to select Histogram."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 95, "content": "Falcon™ 4-CLHS Series \nOperational Reference  •  39 \n \n \nPixel values should all be above zero. Note that the Black Level setting is not applied during calibration. Step 3. Set the FPN Correction Mode to Calibration and select the required user set from FPN Correction Active \nSet list. Step 4. Set the Target Exposure Range Min and Target Exposure Range Max to values that correspond to the \nexposure range required for the camera’s expected operating conditions. Step 5. If Optical Black Reference correction is required, set the feature to Active. Step 6. Next to Calibration FPN, click Press to perform the calibration. Step 7. If required, verify the Optical Black Reference Calibration Offset setting; set the FPN Correction Mode to \nOff and Black Level to 0."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 96, "content": "40  •  Operational Reference \nFalcon™ 4-CLHS Series \n \nGrab an image and use a histogram to verify that pixel values are above zero; if necessary, adjust the Optical \nBlack Reference Calibration Offset. Higher offset values may be required when the camera is operating at very \nhigh temperature and frame rates. Set the FPN Correction Mode back to Calibration and use the Calibration FPN \ncommand to recalculate the calibration and adjust the offset until the result is satisfactory. Step 8. If the calibration is satisfactory, in the Save FPN Calibration feature click Press to save the calibration to \nthe selected user set. For each gain setting required perform and save the calibration; coefficients for each gain setting are saved in the \nuser set (gain settings for which no calibration is done use default coefficients)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 97, "content": "Step 9. To load this user set when resetting or powering on the camera, select the Camera Information category, \nand next to the Power-up Configuration feature, click Setting. In the Power-up Configuration dialog box, select the required user set in both Camera Power-up configuration and \nLoad / Save configuration lists, then select Save. This ensures that the camera loads the saved parameters the next time the camera is turned on. The FPN Correction coefficient file is a standard 16-bit TIFF file for both 8-bit and 10-bit acquisition modes. Falcon™ 4-CLHS Series \nOperational Reference  •  41 \nDefective Pixel Replacement \nThe Pixel Replacement algorithm is based on a predefined bad pixel map (as an XML file), either supplied by the \nfactory (file loaded as Factory Map) or generated by the user (file uploaded as User Map 1–except M2240 model)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 98, "content": "NOTE Identifying bad pixels is left to the user’s discretion, but Teledyne DALSA technical support can provide \nguidance. The following XML code sample forms the template for the user to build bad pixel maps for any of their Falcon4 \ncameras. Example User Defective Pixel Map XML File The following example shows the required components of the defective pixel map file. Each bad pixel position \n(relative to the image origin which is the upper left corner), must be identified by the XML statement:  \n<DefectivePixel OffsetX=”number” OffsetY=”number”/>  \n \nNOTE \nPixels must be sorted in the XML file by ascending row (OffsetY); within each row, pixels must also be sorted in \nascending order horizontally (OffsetX)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 99, "content": "That is, the order is from top-left of the image to the bottom-right. The pixel format (whether 8-bit or 10-bit) is handled transparently, thus requires no special consideration by the \nuser. This example XML listing has four “bad” pixels identified (maximum number of entries is model dependent). The \nalgorithm descriptions that follow defines the rules used by the Falcon4 firmware to replace an identified bad \npixel. <?xml version=\"1.0\" encoding=\"utf-8\"?> \n \n<! --Example"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 100, "content": "User Defective Pixel Map--> \n<!--maximum 1022 coordinates--> \n<!--filename: Falcon4ExampleBadPixels.xml--> \n \n<Coordinates> \n \n  <DefectivePixel OffsetX=\"100\" OffsetY=\"0\"/> \n  <DefectivePixel OffsetX=\"468\" OffsetY=\"50\"/> <DefectivePixel OffsetX=\"223\" OffsetY=\"600\"/> \n  <DefectivePixel OffsetX=\"800\" OffsetY=\"600\"/> \n  \n</Coordinates> \n \nNOTE \nThe Falcon4 automatically adjusts the defective pixel map if binning is enabled by combining adjacent row \ndefects. Pixel replacement occurs in the processing chain before horizontal binning. The user defective pixel map can be downloaded or uploaded to the camera using the features available in the \nFile Access Control category. The factory map can also be downloaded."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 101, "content": "42  •  Operational Reference \nFalcon™ 4-CLHS Series \nDefective Pixel Replacement Algorithm (M2240, M4400, M4480) The replacement algorithm follows a few basic rules as defined below, which in general provides satisfactory \nresults. Single bad pixel in a sensor line with a good adjacent pixel \n• \nA defective pixel is replaced by the following good pixel if previous pixel is bad or not existent. • \nOr a defective pixel is replaced by the previous good pixel. Sensor Row\npix1\npix2\npix3\npix5\npix6\npix7\npix4\npix0\n \nBad pixel in a sensor line with bad adjacent pixels \n• \nReplace bad pixel with the corresponding pixel of the previous line. • \nDo nothing when the neighboring pixels are also bad."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 102, "content": "Sensor Row “n+1”\npix1\npix2\npix3\npix5\npix7\npix0\npix1\npix2\npix3\npix5\npix6\npix7\npix4\npix0\npix4\npix6\nSensor Row “n”\nRemains \nDefective\n \nDefective Pixel Replacement Algorithm (M6200, M8200) The replacement algorithm follows a few basic rules as defined below. The replacement of a defective pixel is \ndone in the specified order. Note that pixels that are outside an ROI will not be considered to replace a defective \npixel within an ROI. • \nReplace defective pixel (pix5) with the previous pixel (pix4) if it is not defective. • \nReplace defective pixel (pix1) with the next pixel (pix2) if the previous pixel (pix0) is defective or non-existent."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 103, "content": "pix1\npix2\npix3\npix5\npix6\npix7\npix4\npix0\n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  43 \n• \nReplace defective pixel (row n, pix1) with the left diagonal pixel on the previous line (row n-1, pix0) if both \nadjacent pixels are defective. • \nReplace defective pixel (row n, pix5) with the right diagonal pixel on the previous line (row n-1, pix6) if the left \ndiagonal pixel is also defective. pix1\npix3\npix5\npix7\npix0\npix1\npix2\npix3\npix5\npix6\npix7\npix0\npix6\npix4\npix4\npix2\n \n• \nReplace defective pixel (row n, pix1) with the corresponding pixel on the previous line (row n-1, pix1) if both \nleft and right diagonal pixels on the previous line are defective. • \nDo nothing when all the neighboring pixels are defective. pix1\npix3\npix7\npix0\npix1\npix3\npix7\npix4\npix2\npix6\npix5\nremains defective\npix4\npix5\npix6\npix0\npix2\n \n \n \n44  •  Operational Reference \nFalcon™ 4-CLHS Series \nFlat Field Category \nThe Falcon4 Flat Field controls, includes parameters to perform flat field calibration of the sensor."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 104, "content": "Once \ncalibration is done and saved, flat-field correction can be activated. Flat Field Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nFlat Field Correction \nMode \nflatfieldCorrectionMode \nSets the mode for the Flat Field correction. See flatfieldCorrectionType below. 1.00 \nBeginner \nDFNC \nOff \nOff \nFlat Field Correction is disabled. Active \nActive \nFlat Field Correction is enabled. Calibration \nCalibration \nFlat Field Correction in calibration mode."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 105, "content": "Flat Field Correction \nCurrent Active Set \nflatfieldCorrectionCurrentActiveSet \nSpecifies the current set of Flat Field coefficients \nto use. 1.00 \nBeginner \nDFNC \nFactory Set \nFactoryFlatfield \nSets the factory Flat Field coefficient table as the \ncurrent Flat Field. User Set 1 \nUserFlatfield1 \nSets User Flat Field 1 coefficient table as the \ncurrent Flat Field. Flat Field Correction \nCalibration Dark \nflatfieldCorrectionCalibrationDark \nPerform a dark calibration. This is done before \nthe bright calibration. This calibration requires a \ndark acquisition (as little light on the sensor as \npossible)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 106, "content": "1.00 \nExpert \nDFNC \nFlat Field Correction \nCalibration Bright \nflatfieldCorrectionCalibrationBright \nPerform a bright calibration. This is done after \nthe dark calibration. This calibration requires a \nbright featureless acquisition that is not \nsaturated. 1.00 \nExpert \nDFNC \nFlat Field Calibration \nTarget \nflatfieldCalibrationTarget \nSets the target value for the bright calibration. 1.00 \nExpert \nDFNC \nSave Calibration \nflatfieldCorrectionCalibrationSave \nSave the calibration results of the \nflatfieldCorrectionCalibrationDark and/or \nflatfieldCorrectionCalibrationBright operations to \nthe current active set. 1.00 \nExpert \nDFNC \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  45 \nReset Calibration \nflatfieldCorrectionCalibrationResetCoefficients \nReset the current calibration coefficients to \nfactory defaults."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 107, "content": "1.00 \nExpert \nDFNC \nFlat Field Correction \nAlgorithm  \nflatfieldCorrectionAlgorithm \nSpecifies the Flat Field correction algorithm to \nuse. 1.00 \nGuru \nDFNC \nSet Target \nTarget \nEach pixel is gained up to the value specified in \nthe Flat Field Calibration Target feature. Flat Field Correction Type \nflatfieldCorrectionType \nSpecifies the Flat Field correction type. 1.00 \nGuru \nDFNC \nLine-Based \nLineBase \nFlat field correction is based on the average of \nlines of gain and offset coefficients where \ncorrections are applied to each pixel in the \ncolumn. 46  •  Operational Reference \nFalcon™ 4-CLHS Series \nLens Shading Correction Category \nThe Falcon4 Lens Shading Correction controls, as shown by CamExpert, has parameters to configure the lens \nshading correction features. Lens Shading Correction Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nLens Shading Correction Mode \nlensShadingCorrectionMode \nSets the mode for the lens shading \ncorrection."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 108, "content": "This feature cannot be changed \nwhile the Lens Shading Correction is Active \nin Cycling Preset. 1.01 \nExpert \nDFNC \nOff \nOff \nLens Shading Correction is Disabled \nActive \nActive \nLens Shading Correction is Enabled \nCalibration \nCalibration \nWhen selected, the camera is configured for \nLens Shading correction calibration. Some \nprocessing will be disabled even if the \nassociated feature is enabled. Lens Shading Correction Active Set \nlensShadingCorrectionCurrentActiveSet \nSpecifies the current set of Lens Shading \nCoefficients to use. 1.01 \nExpert \nDFNC  \nFactory \nShadingCoefficients0 \nSets the factory coefficients as current. User Shading Coefficients 1 \nShadingCoefficients1 \nSets User Shading Coefficients set 1 as \ncurrent."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 109, "content": "User Shading Coefficients 2 \nShadingCoefficients2 \nSets User Shading Coefficients set 2 as \ncurrent. 1.03 \nUser Shading Coefficients 3 \nShadingCoefficients3 \nSets User Shading Coefficients set 3 as \ncurrent. 1.03 \nUser Shading Coefficients 4 \nShadingCoefficients4 \nSets User Shading Coefficients set 4 as \ncurrent. 1.03 \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  47 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nLens Shading Calibration \nlensShadingCorrectionCalibrationBright \nPerform a bright calibration for lens shading \ncorrection. This calibration requires a bright \nfeatureless acquisition that is not saturated. (70% illumination is recommended)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 110, "content": "1.01 \nExpert \nDFNC \nSave Calibration \nlensShadingCorrectionCalibrationSave \nSave the calibration results of the \nlensShadingCorrectionCalibrationBright \noperations to the active set. 1.01 \nExpert \nDFNC \nReset Coefficients \nlensShadingResetCoefficients \nReset lens shading coefficients to pass-\nthrough. 1.01 \nExpert \nDFNC \n \nLens Shading Calibration \nIt is recommended that a Lens Shading Calibration procedure be done for any Falcon4/lens combination. Lens \nShading Calibration eliminates any lens vignetting in the image corners or any other shading differences across \nthe image field. It will allow using a lens with a slightly smaller image circle that does not quite evenly expose the \nwhole sensor. For more information refer to section Choosing a Lens with the Correct Image Circle."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 111, "content": "CamExpert allows quick calibration by the user: 1. Set the Lens Shading Correction Mode to Calibration*. 2. With a bright featureless acquisition that is not saturated (70% illumination is recommended), click Press next \nto Lens Shading Calibration to execute the calibration. 3. If calibration is satisfactory, select a user set, and next to Save Calibration click Press to save the coefficients \nto the user set."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 112, "content": "The features for the Lens Shading Correction category can also be accessed by the user designed application. The lens shading correction user set can be downloaded or uploaded to the camera using the features available \nin the File Access Control category. * The Calibration mode will not be selectable unless the following Cycling Preset features are both Off: Cycling \nPreset Mode and Features Activation Mode for Lens Shading Correction (shown below). 48  •  Operational Reference \nFalcon™ 4-CLHS Series \nLUT Category \nThe LUT category, as shown by CamExpert, groups parameters used to configure lookup tables LUT on \nmonochrome cameras. LUT Feature Description \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nLUT Mode \nlutMode \nSets the enable state of the LUT module (Lookup Table)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 113, "content": "1.02 \nExpert \nDFNC \nOff \nOff \nDisables the LUT. Active \nActive \nEnables the LUT module. LUT Type \nlutType \nDisplays the LUT type of the currently selected Lookup Table. 1.02 \nExpert \nDFNC \nUser Defined \nUserDefined \nUses the user programmable LUT. LUT Selector \nLUTSelector \nSelects which LUT to control and adjust features. 1.02 \nGuru \nDFNC \nLuminance 1 \nLuminance1 \nLuminance 1 is under control."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 114, "content": "LUT Size \nlutSize \nSpecify the LUT size of the selected LUT (Lookup Table). 1.02 \nGugu \nDFNC \n10 Bits/Pixel \nBpp10 \n10 bits per pixel. LUT Index \nLUTIndex \nSelect the LUT index. 1.02 \nGuru \nDFNC \nLUT Value \nLUTValue \nReturns the value at specified LUT index entry of the LUT \nselected by the LUT Selector feature. 1.02 \nGuru \nDFNC \nLUT Value All \nLUTValueAll \nAccesses all the LUT coefficients in a single access without \nusing individual LUT indices. 1.02 \nGuru \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  49 \nLookup Table (LUT) Overview \nThe Falcon4-CLHS cameras include a user programmable LUT (lookup table) as a component of its embedded \nprocessing features."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 115, "content": "A LUT is used for operations such as gamma adjustments, invert function and threshold \nprocesses. The camera LUT tables depend on the sensor (see feature LUT Size) and is illustrated in the following figure. Pixel data from the sensor is passed through the LUT memory array, where the new programmed pixel value is \nthen passed to the camera output circuit. The LUT data table is stored along with other parameters with the user \nconfiguration function.  \n. . .\n. . .\n0\n1\n2\n3\n1020\n1023\n1022\n1021\n1023\n1022\n1021\n1020\n2\n1\n0\n3\nOutput\nSensor\nPixel \nData\nSimplified LUT Block Diagram\n10-bit Input : 10-bit Output\nLUT Programmed \nas Invert Function\n \nSimplified Example 10-bit to 10-bit LUT Block Diagram \nLUT Size vs. Pixel Format \nThe LUT size will correspond to the camera’s sensor pixel size; for the current Falcon4-CLHS standard firmware, \nthis is 10 bits per pixel, i.e., 1024."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 116, "content": "All camera processing is performed at the 10-bit sensor pixel format of the \ncamera, while the end user chooses the pixel format (8-bit or 10-bit format) to output. The default neutral LUT programming is as follows:  \n• \nWith Pixel Format = Mono 10, the default LUT data value is equal to the LUT value for each index. This is a \nlinear LUT that does not modify the sensor data. • \nWith Pixel Format = Mono 8, the LUT remains to be a 10 bit in 10 bit out. The conversion to 8 bit occurs \nafter the LUT. LUT data is selected as a user file uploaded using the File Access controls."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 117, "content": "Refer to the Sapera documentation \nfor information about the SapLut Class. Note that a SapLut file can be uploaded but cannot be read back. 50  •  Operational Reference \nFalcon™ 4-CLHS Series \nImage Format Control Category \nThe Image Format controls, as shown by CamExpert, groups parameters used to configure camera pixel format, \nimage cropping, etc. Image Format Control Feature Description \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nPixel Format \nPixelFormat \nPixel Format. \n1.00 \nBeginner \nMono 8 \nMono8 \nPixel Format Mono8 \nMono 10 \nMono10 \nPixel Format Mono10 \nWidth Max \nWidthMax \nThe maximum image width is the dimension calculated after any \nother function changing the horizontal dimension of the image. 1.00 \nBeginner \nHeight Max \nHeightMax \nThe maximum image height is the dimension calculated after \nany other function changing the vertical dimension of the image. 1.00 \nBeginner \nHorizontal Offset \nOffsetX \nOutput image horizontal offset from the origin (always zero)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 118, "content": "1.00 \nBeginner \nVertical Offset \nOffsetY \nOutput image vertical offset from the origin (always zero). 1.00 \nBeginner \nWidth \nWidth \nWidth of the image provided by the device (in pixels). 1.00 \nBeginner \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  51 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nHeight \nHeight \nHeight of the image provided by the device (in pixels). 1.00 \nBeginner \nTest Pattern \nTestImageSelector \nSelect an internal Test Pattern. 1.00 \nBeginner \nOff \nOff \nImage is from the camera sensor. Grey Horizontal Ramp \nGreyHorizontalRamp \nImage is filled horizontally with an image that goes from the \ndarkest possible value to the brightest."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 119, "content": "Grey Vertical Ramp \nGreyVerticalRamp \nImage is filled vertically with an image that goes from the \ndarkest possible value to the brightest. Grey Diagonal Ramp \nGreyDiagonalRamp \nImage is filled diagonally with an image that goes from the \ndarkest possible value to the brightest. Constant \nConstant \nImage is filled completely with the pixel value set by the \nTestImageValue feature. Test Image Value \nTestImageValue \nPixel value for Constant test pattern. 1.00 \nBeginner \nBinning Selector \nbinningSelector \nSelect how the Horizontal and Vertical Binning is done. The \nBinning function can occur in the Digital domain of a device or at \nthe actual sensor."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 120, "content": "1.01 \nBeginner \nDFNC \nMixed \nMixed \nVertical binning is done inside the Sensor itself; horizontal \nbinning is done inside the device but with a digital processing \nfunction. Binning Mode \nbinningMode \nBinning mode. 1.01 \nBeginner \nDFNC \nAverage \nAverage \nThe responses from the individual pixels are averaged, resulting \nin increased signal to noise ratio. Binning Horizontal \nBinningHorizontal \nNumber of horizontal pixels to combine together. This reduces \nthe horizontal resolution of the image. Note, if horizontal binning \nis applied, vertically binning is also automatically applied."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 121, "content": "1.01 \nExpert \nBinning Vertical \nBinningVertical \nNumber of vertical pixels to combine together. This reduces the \nvertical resolution of the image. Note, if vertical binning is \napplied, horizontal binning is also automatically applied. 1.01 \nExpert \nMultiple ROI Mode \nmultipleROIMode \nEnable the Multiple ROI (Region of Interest) per image feature. The ROI Count is set by the ROI Count Vertical feature. 1.02 \nExpert \nDFNC \nOff \nOff \nSingle ROI per image."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 122, "content": "Active \nActive \nThe ROI per image feature is active. ROI Count Vertical \nmultipleROICountVertical \nSpecifies the number of possible ROI (Region of Interest) \navailable in an acquired image. Two is minimum, while the \nmaximum is device specific. 1.02 \nExpert \nDFNC \nROI Selector \nmultipleROISelector \nSelect an ROI (Region of Interest) when Multiple ROI Mode is \nenabled. Selector range is from 1 to the ROI Count Vertical \nvalue. 1.02 \nExpert \nDFNC \nROI 1 \nROI 2 \n… \nROI 32 \nroi1_1 \nroi1_2 \n... \nroi1_32 \n \nROI Offset Y \nmultipleROIOffsetY \nVertical offset (in pixels) from the origin to the selected ROI \n(Region of Interest)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 123, "content": "1.02 \nExpert \nDFNC \nROI Height \nmultipleROIHeight \nHeight of the selected ROI (Region of Interest) provided by the \ndevice (in pixels). 1.02 \nExpert \nDFNC \n \n \n52  •  Operational Reference \nFalcon™ 4-CLHS Series \nWidth and Height Features for Partial Scan Control \nWidth and Height controls along with their respective offsets, allow the Falcon4-CLHS to grab a region of interest \n(ROI) within the full image frame. Besides eliminating post acquisition image cropping done by software in the \nhost computer, a windowed ROI grab reduces the bandwidth required since fewer pixels are transmitted. NOTE Any reduction of the camera’s acquisition area from its maximum must be matched by the same reduction in \nthe frame grabber’s buffer dimensions. The Teledyne DALSA CLHS frame grabber will generate “Buffer \nIncomplete” errors when the buffer dimensions do not match the cropped acquisition."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 124, "content": "Vertical Cropping (Partial Scan)  \nThe Height and Vertical Offset features, used for vertical cropping, reduce the number of video lines grabbed for a \nframe. By not scanning the full height of the sensor, the maximum possible acquisition frame rate is \nproportionately increased, up to the model maximum. The following figure is an example of a partial scan acquisition using both Height and Vertical Offset controls. The \nVertical Offset feature defines at what line number from the sensor origin to acquire the image. The Height feature \ndefines the number of lines to acquire (to a maximum of the remaining frame height). Note that only the partial \nscan image (ROI) is transmitted to the host computer."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 125, "content": "Vertical Offset\nHeight\nPartial Image Grab\n \nPartial Scan Illustration \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  53 \nMaximum Frame Rate Examples \nExample frame rates for M2240, M4400 and M4480 models  \nConditions: \n• \n8-bit \n• \nMinimum Exposure Time: 5 µs \n• \nFast Readout Mode: Active (not available in M2240 and M4400 models) \n• \nExposure Alignment: Synchronous \n \nVertical Lines Acquired \nM2240 \nM4400 \nM4480* \n2496 \nNA \n335 \n600 \n1248 \n1 206 \n664 \n1 187 \n624 \n2 358 \n1 303 \n2 325 \n312 \n4 524 \n2 512 \n4 465 \n160 \n8 196 \n4 587 \n8 064 \n80 \n14 285 \n8 130 \n14 084 \n40 \n22 727 \n13 157 \n22 222 \n24 \n29 411 \n17 543 \n29 411 \n16 \n35 714 \n20 833 \n34 482 \n8 \n43 478 \n26 315 \n41 666 \n*frame rate will be lower if camera is set to cycle exposure time \nExample frame rates for M6200 and M8200 models  \nConditions:  \n• \n8-bit \n• \nMinimum Exposure Time: 8 µs \n• \nExposure Alignment: Synchronous.  \n• \nROI centered on the imaging area of the sensor \n \nVertical Lines Acquired \nM6200, M8200 \n8192* \n91 \n6144 \n121 \n4096 \n181 \n1024 \n711 \n512 \n1 386 \n256 \n2 637 \n128 \n4 805 \n40 \n11 053 \n16 \n17 127 \n8 \n20 967 \n4 \n23 615 \n* Does not apply to M6200. 54  •  Operational Reference \nFalcon™ 4-CLHS Series \nHorizontal Cropping (Partial Scan)  \nThe Width and Horizontal Offset parameters are used to crop the acquisition horizontally by grabbing fewer pixels \non each horizontal line. Horizontal offset (OffsetX) defines the start of the acquired video line while horizontal \nwidth (Width) defines the number of pixels per line. Horizontal Crop\nHorizontal Offset\nWidth\n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  55 \nMultiple ROIs \nThe Falcon4-CLHS allows for multiple regions of interest to be acquired. You can define up to 32 regions of interest in the ROI Count Vertical feature. ROIs are defined as a number of \nlines to grab at a given offset."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 126, "content": "The ROIs retain the full width of the sensor, unless you specify a Width and a \nHorizontal Offset, in which case they apply to all ROIs. To define multiple ROIs:  \n• \nSet Multiple ROI Mode to Active. • \nSpecify the number of regions in ROI Count Vertical. • \nSelect an ROI in the ROI Selector, and specify the line number from the sensor origin (ROI Offset Y) and \nthe number of lines to grab (ROI Height). • \nRepeat for each ROI. • \nIn the Image Buffer and ROI category in the Board, modify the Image Width (in Pixels) and Image Height \n(in Lines) features to match your image width and total number of lines of your ROIs."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 127, "content": "ROI 1 - Offset Y\nROI 1 - Height\nROI 2 - Offset Y\nROI 2 - Height\n \n \n \n56  •  Operational Reference \nFalcon™ 4-CLHS Series \nBinning Function \nBinning is the process where the charge on two adjacent pixels is combined. This results in increased light \nsensitivity since there is twice the sensor area to capture photons. The sensor spatial resolution is reduced but \nthe improved low-light sensitivity plus lower signal-noise ratio may solve a difficult imaging situation. The user can \nevaluate the results of the binning function on the Falcon4 by using CamExpert. Horizontal and vertical binning functions are applied together, by factors of 2 in each axis (2x2). Specifically with \nbinning activated, a nominal 640x480 image is reduced to 320x240."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 128, "content": "Vertical binning is performed in-sensor and horizontal binning digitally; therefore with binning there is an increase \nin acquisition frame rate (maximum frame rate at full resolution, minimum exposure time = 1206.2 fps (M4480) \nand 664.4 fps (M4400)). The following graphic illustrates the horizontal and vertical binning mechanism. 1\n640\n639\n4\n3\n2\n1\n2\n320\nHorizontal Binning\nby 2\nLine\n1\nLine\n2\nLine\n3\nLine\n4\nLine\n479\nLine\n480\nLine\n1\nLine\n2\nLine\n240\nRepeated for each \nline of pixels\nRepeated for each column of pixels\nVertical Binning\nby 2\n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  57 \nInternal Test Pattern Generator \nThe camera includes a number of internal test patterns which easily confirm camera installations, without the \nneed for a camera lens or proper lighting. Use CamExpert to easily enable and select a test pattern from the drop \nmenu while the camera is not in acquisition mode. Select live grab to see the pattern output. The Test Patterns are: \n• \nGrey Horizontal ramp: Image is filled horizontally with an image that goes from the darkest possible value to \nthe brightest."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 129, "content": "• \nGrey Vertical ramp: Image is filled vertically with an image that goes from the darkest possible value to the \nbrightest. • \nGrey Diagonal Ramp Moving: Image is filled diagonally with an image that goes from darkest to brightest, \nand that moves when grabbing. • \nConstant: Image filled completely with the pixel value set by the Test Image Value feature. 58  •  Operational Reference \nFalcon™ 4-CLHS Series \nTransport Layer Category \nThe Transport Layer Control Features are related to CLHS specification. Transport Layer Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCLHS Discovery Mode \nclhsDiscovery \nDisable CLHS Discovery if not implemented in frame \ngrabber. If disabled then camera will enable image \ntransmitters as soon as the cable is connected."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 130, "content": "1.00 \nDFNC \nBeginner \nDiscovery Disabled \nDiscoveryDisable \nDiscovery Disabled \n \nDiscovery Enabled \nDiscoveryEnable \nDiscovery Enabled \n \nNext CLHS Device \nConfiguration \nclhsNextDeviceConfig \nSelect next CLHS device configuration from valid list. Reboot or reconnect cable to activate. 1.00 \nDFNC \nBeginner \nOne Cable Four Lanes \nOneCableFourLanes \nOne cable with 4 data lanes (M2240, M4400 models). One Cable Seven Lanes \nOneCableSevenLanes \nOne cable with 7 data lanes (M4480, M6200, M8200 \nmodels). CLHS 64b/66b Receive Error \nCount \nclhsErrorCount \nCLHS 64b/66b Receive Error Count \n1.00 \nDFNC \nGuru \nRefresh CLHS 64b/66b Receive Error Count \nclhsErrorCountRefresh \nRefresh CLHS 64b/66b Receive Error Count \n1.00 \nDFNC \nGuru \nReset Receive Error Count \nclhsErrorCountReset \nReset current CLHS 64b/66b Receive Error Count to \nZero \n1.00 \nDFNC \nGuru \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  59 \nAcquisition and Transfer Control Category \nThe Acquisition and Transfer controls as shown by Cam<PERSON>xpert, has parameters used to configure the optional \nacquisition modes of the device."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 131, "content": "Acquisition and Transfer Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nAcquisition Mode \nAcquisitionMode \nDefines the way that frames are acquired. \n1.00 \nBeginner \nContinuous \nContinuous \nFrames are captured continuously until stopped with the \n\"Acquisition Stop\" command. Acquisition Start \nAcquisitionStart \nCommands the camera to start sending image data. 1.00 \nBeginner \nAcquisition Stop \nAcquisitionStop \nCommands the camera to stop sending image data at the \nend of the current line. 1.00 \nBeginner \nAcquisition Status \nAcquisitionStatus \nIndicates whether the camera has been commanded to \nsend image data. 1.00 \nBeginner \nNot Acquiring \nNotAcquiring \nNot Acquiring \n \nAcquiring Acquiring"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 132, "content": "Acquiring \n \n \n \n60  •  Operational Reference \nFalcon™ 4-CLHS Series \nAcquisition Buffering \nAll acquisitions are internally buffered and transferred as fast as possible to the host system. This internal buffer \nallows uninterrupted acquisitions no matter of any transfer delays that might occur. Only when the internal \nacquisition buffer is consumed would an Image Lost Event be generated. Features that cannot be changed during a Transfer \nThe following features cannot be changed during an acquisition or when a transfer is connected. Feature Category \nFeatures Locked During a Sapera Transfer \nCAMERA INFORMATION \nUserSetLoad, deviceBIST, DeviceReset \nCAMERA CONTROL \nAcquisitionFrameRate, ExposureMode, exposureAlignment \nGainSelector \nDIGITAL IO CONTROL \nTriggerSelector, TriggerMode, triggerFrameCount \nTriggerSource, TriggerDelay, TriggerOverlap- \nDATA PROCESSING \nFPNCorrectionMode \nFPNCorrectionActiveSet \nFPNCalibrationTargetExposureMin \nFPNCalibrationTargetExposureMax \nFPNCalibrationOpticalBlackReference \nFPNCalibrationOpticalBlackReferenceOffset \nFPNCalibrationCalibrate \nFPNCalibrationSave \nflatfieldResetCoefficients \ndefectivePixelReplacementMode \ndefectivePixelReplacementMapCurrentActiveSet \nlensShadingCorrectionMode, lensShadingCorrectionCurrentActiveSet \nlensShadingCorrectionCalibrationSampleSize \nlensShadingCorrectionCalibrationBright \nlensShadingResetCoefficients \nDEVICE COUNTER AND TIMER CONTROL \nNA \nIMAGE FORMAT CONTROL \nPixelFormat \nOffsetX (except within the Cycling Mode) OffsetY (except within the Cycling Mode) \nWidth, Height \nTestImageSelector \nBinningHorizontal \nBinningVertical \nACQUISITION AND TRANSFER CONTROL \nDeviceRegistersStreamingStart \nDeviceRegistersStreamingEnd \nFILE ACCESS CONTROL \nNA \n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  61 \nDevice Counter and Timer Control Category"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 133, "content": "The Device Counter and Timer Control category, as shown by CamExpert, groups parameters used to configure \nacquisition counters and timers for various input lines and signal edge detection. Device Counter and Timer Control Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCounter Selector \ncounterSelector \nSelects the counter to configure. 1.00 \nBeginner \nDFNC \nCounter 1 \nCounter1 \nSelect counter 1  \nCounter Mode \ncounterMode \nEnable the selected Counter.  \n1.00 \nBeginner \nDFNC \nOff \nOff \nThe selected Counter is Disabled. Active \nActive \nThe selected Counter is Enabled. 62  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCounter Status \ncounterStatus \nReturns the current state of the counter. 1.00 \nBeginner \nDFNC \nCounter Idle \nCounterIdle"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 134, "content": "The counter is idle. Counter Trigger Wait \nCounterTriggerWait The counter is waiting for a start trigger. Counter Active \nCounterActive The counter is counting for the specified duration. Counter Completed \nCounterCompleted The counter reached the CounterDuration count. Counter Overflow \nCounterOverflow The counter reached its maximum possible count. Counter Start Source \ncounterStartSource \nSelect the counter start source. Counter increments from 0 \nto the value of the counterDuration feature. 1.00 \nBeginner \nDFNC \nNo Source \nOff \nCounter is stopped."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 135, "content": "Exposure Start \nExposureStart \nCounter starts on the reception of the Exposure Start event \n \nExposure End \nExposureEnd \nCounter starts on the reception of the Exposure End event. Readout Start \nReadoutStart \nCounter starts on the reception of the Readout Start event. Readout End \nReadoutEnd \nCounter starts on the reception of the Readout End event. Frame Start \nFrameStart \nCounter starts on the reception of the Frame Start event. Valid Frame Trigger \nValidFrameTrigger \nCounter starts on the reception of the Valid Frame Trigger. Invalid Frame Trigger \nInvalidFrameTrigger \nCounter starts on the reception of the Invalid Frame \nTrigger."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 136, "content": "Line 1 \nLine1 \nCounter starts on the specified transitions on Line 1 (based \non the counterIncrementalLineActivation feature setting). Line 2 \nLine2 \nCounter starts on the specified transitions on Line 2 (based \non the counterIncrementalLineActivation feature setting). CLHS In \nLinkTrigger0 Counter starts on the transitions of LinkTrigger0 (based on \nthe counterIncrementalLineActivation feature setting). Timer 1 End \nTimer1End \nCounter starts on the reception of the Timer 1 End event. Counter 1 End \nCounter1End \nCounter starts on the reception of the Counter 1 End event."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 137, "content": "Counter Start Line \nActivation \ncounterStartLineActivation \nSpecify the edge transition on the selected line that starts \nthe counter. \n1.00 \nBeginner \nDFNC \nRising Edge \nRisingEdge \nStarts counting on rising edge of the selected trigger signal. Falling Edge \nFallingEdge \nStarts counting on falling edge of the selected trigger \nsignal. Any Edge \nAnyEdge \nStarts counting on the falling or rising edge of the selected \ntrigger signal. Counter Incremental \nSource \ncounterIncrementalSource \nSelect the event source which increments the counter. The \nEvent Control section provides details and timing diagrams \nfor the supported events. 1.00 \nBeginner \nDFNC \nOff \nOff \nCounter is stopped."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 138, "content": "Exposure Start \nExposureStart \nCounts the number of Exposure Start events. ExposureEnd \nExposureEnd \nCounts the number of Exposure End events. Readout Start \nReadoutStart \nCounts the number of Readout Start events. Readout End \nReadoutEnd \nCounts the number of Readout End events. Frame Start \nFrameStart \nCounts the number of Frame Start events. Valid Frame Trigger \nValidFrameTrigger \nCounts the number of Valid Frame Triggers. MultiFrame End Trigger \nFrameBurstEnd \nCounts the number of multi-frame end triggers. CLHS In \nLinkTrigger0 \nCounts the number of transitions on LinkTrigger0. Line 1 \nLine1 \nCounts the number of transitions on Line 1. Line 2 \nLine2 \nCounts the number of transitions on Line 2. Invalid Frame Trigger \nInvalidFrameTrigger \nCounts the number of rejected frame triggers. Internal Clock \nInternalClock \nCounts the number of microsecond ticks."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 139, "content": "Timer 1 End \nTimer1End \nCounts the number of Timer 1 End events. Falcon™ 4-CLHS Series \nOperational Reference  •  63 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCounter Incremental Line \nActivation \ncounterIncrementalLineActivation \nSelects the counter signal activation mode. The counter \nincrements on the specified signal edge. 1.00 \nBeginner \nDFNC \nRising Edge \nRisingEdge \nIncrement the counter on the rising edge of the signal. Falling Edge \nFallingEdge \nIncrement the counter on the falling edge of the signal. Any Edge \nAnyEdge \nIncrement the counter on the falling or rising edge of the \nsignal."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 140, "content": "Counter Duration \ncounterDuration \nSets the duration (or number of events) before the \nCounterEnd event is generated. 1.00 \nBeginner \nDFNC \nCounter Reset Source \ncounterResetSource \nSelects the signal source to reset the counter. After a reset \nthe counter waits for the next countStartSource signal or \nevent. 1.00 \nBeginner \nDFNC \nOff \nOff \nReset on reception of the Reset Icommand. Exposure Start \nExposureStart \nReset on reception of the Exposure Start event. Exposure End \nExposureEnd \nReset on reception of the Exposure End event."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 141, "content": "Readout Start \nReadoutStart \nReset the counter on the reception of the Readout Start \nevent. Readout End \nReadoutEnd \nReset the counter on the reception of the Readout End \nevent. Frame Start \nFrameStart \nReset on reception of the Frame Start. Valid Frame Trigger \nValidFrameTrigger \nReset on reception of the Valid Frame Trigger. Invalid Frame Trigger \nInvalidFrameTrigger \nReset on reception of the Invalid Frame Trigger. FrameBurst End \nFrameBurstEnd \nReset on reception of the Frame Burst end. Line 1 \nLine1 Reset counter on the specified transition on line 1. Line 2 \nLine2 \nReset counter on the specified transition on line 2. \nCLHS In \nLink0Trigger \nReset on reception of CLHS In (Link0Trigger). Timer 1 End \nTimer1End \nReset on reception of the Timer 1 End. Counter 1 End \nCounter1End Reset on the reception of the Counter 1 End."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 142, "content": "Counter Reset Line \nActivation \ncounterResetLineActivation \nSpecify the edge transition on the selected line that resets \nthe selected counter. 1.00 \nBeginner \nDFNC \nRising Edge \nRisingEdge \nReset counter on rising edge of the selected signal. Falling Edge \nFallingEdge \nReset counter on falling edge of the selected signal. Any Edge \nAnyEdge \nReset counter on the falling or rising edge of the selected \nsignal. Counter Value \ncounterValue \nRead the current value of the selected counter. 1.00 \nBeginner \nDFNC \nCounter Value at Reset \ncounterValueAtReset \nReads the value of the selected counter when it was reset \nby a trigger or by an explicit Counter Reset command."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 143, "content": "1.00 \nBeginner \nDFNC \nCounter Reset \ncounterReset \nResets the selected counter to zero. The counter starts \nimmediately after the reset. To disable the counter, set the \nCounter Start Source feature to Off.  \n1.00 \nBeginner \nDFNC \n \nTimer Selector \ntimerSelector \nSelects which timer to configure. 1.00 \nBeginner \nDFNC \nTimer 1 \nTimer1 \nTimer 1 selected \nTimer Mode \ntimerMode \nEnable and activate the selected Timer. 1.00 \nBeginner \nDFNC \nOff Off \nThe selected Timer is Disabled. Active \nActive \nThe selected Timer is Enabled. 64  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nTimer Status \ntimerStatus \nReturns the current state of the timer. 1.00 \nBeginner \nDFNC \nTimer Idle \nTimerIdle The timer is idle. Timer Trigger Wait \nTimerTriggerWait The timer is waiting for a start trigger."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 144, "content": "Timer Active \nTimerActive The timer is counting for the specified duration. Timer Completed \nTimerCompleted The timer reached the specified duration. Timer Start Source \ntimerStartSource \nSelect the trigger source to start the timer. 1.00 \nBeginner \nDFNC \nTimerReset Cmd \nOff \nStarts with the reception of the TimerReset Icommand. Exposure Start \nExposureStart \nStart Timer on Exposure Start event. ExposureEnd \nExposureEnd \nStart Timer on Exposure End event. Readout Start \nReadoutStart \nStart Timer on Readout Start event. Readout End \nReadoutEnd \nStart Timer on Readout End event. Frame Start \nFrameStart \nStart Timer on Frame Start event. Valid Frame Trigger \nValidFrameTrigger \nStart Timer on Valid Frame Trigger event."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 145, "content": "Frame Start \nFrameStart \nStart Timer on Frame Start event. Frame Burst End \nFrameBurstEnd \nStart Timer on Frame Burst End event. Line 1 \nLine1 Start Timer on a transition of line 1 event. Line 2 \nLine2 \nStart Timer on a transition of line 2 event. CLHS In \nLinkTrigger0 \nStart Timer on a transition of CLHS In (LinkTrigger0). Timer 1 End \nTimer1End \nStart Timer on Timer  1 End event. Counter 1 End \nCounter1End Start Timer on Counter 1 End event. Timer Line Activation \ntimerStartLineActivation \nSelect the trigger activation mode which starts the timer. 1.00 \nBeginner \nDFNC \nRising Edge \nRisingEdge \nStart timer on rising edge of the selected trigger signal. Falling Edge \nFallingEdge \nStart timer on falling edge of the selected trigger signal."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 146, "content": "Any Edge \nAnyEdge \nStart timer on the falling or rising edge of the selected \nsignal. Timer Delay \ntimerDelay \nSets the duration (in microseconds) of the delay to apply at \nthe reception of a Start Trigger before starting the timer. 1.00 \nBeginner \nDFNC \nTimer Duration \ntimerDuration \nSets the duration (in microseconds) of the timer pulse. When the Timer reaches the timerDuration value, a \nTimerEnd event is generated. 1.00 \nBeginner \nDFNC \nTimer Value \ntimerValue \nReads the current value (in microseconds) of the selected \ntimer. Writing to it to set the initial value."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 147, "content": "1.00 \nBeginner \nDFNC \nTimer Reset \ntimerReset \nResets the timer to 0 and activates the Timer if the \nTimerStartSource is OFF. 1.00 \nBeginner \nDFNC \n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  65 \nCounter and Timer Group Block Diagram \nLine Selector = \nLine 1 to 5\nPhysical\n Line\nEvent Driven \nInput \nInverter\nOutput \ninverter\nSoftware Driven\nPulse \nGenerator\nLineStatus\nTrigger \nLine \nActivation \nTrigger Signal\nTimer\nTimerEnd Event\nCounterEnd Event \nSoftware Trigger \nCmd \nLine \nMode \nInput \nor \nOutput\nInput\nOutput\nTimer and Counter Module\nCounter\nLine \nDebouncer\nEvent Driven \nTrigger \nSource \nTrigger Module\nOutput\nLine\nSource\nSignal Driven\nSoftware Driven\nLine\nDetection\nLevel\n \n \nExample: Counter Start Source = OFF \nCountermode = OFF\nCounter is \nIDLE\nCounter \nOverflow\nCounterEnd Event Generated\nCounter is \nActive\nCounterStartSource = OFF\nCounter \nTrigger Wait\nCounter is incrementing\nCountermode = Active\nCounterResetSource = OFF\nCounterResetSource = Event\nCounter Reset CMD\nCounter Completed\nCounterResetSource = CounterEnd\nCounterStartSource = OFF\n0\n \n• \nThe counter starts on the counterReset Cmd. \n• The counter continues unless a new counterReset Cmd is received, which then restarts the counter at 00. • \nWhen Counter Reset Source = ‘Event’ or ‘CounterEnd’ the counter is reset to 00 but does not restart \ncounting, until the next CounterReset Cmd."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 148, "content": "66  •  Operational Reference \nFalcon™ 4-CLHS Series \nExample: Counter Start Source = CounterEnd (itself) Countermode=OFF\nCounter is \nIDLE\nCounterEnd Event Generated\nCounter is \nActive\nCounterStartSource=\nCounterEnd (itself)\nCounterWait \nTrigger\nCounter is incrementing\nCountermode=Active\nCounter Reset CMD\nCounter \nCompleted\nCounterResetSource=CounterEnd\nCounterStartSource=CounterEnd (itself)\n0\n \n• \nCounter starts when Counter Mode is set to Active. • \nA Counter Reset CMD will reset the counter to 00 and it then continues counting. • \ncounterResetSource must be set to CounterEnd. When the counterValue feature reaches the \ncounterDuration value an event is generated and the counter is reset to 00, then continues."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 149, "content": "Example: CounterStartSource = EVENT and Signal (Edge Base) \nCountermode=OFF\nCounter is \nIDLE\nCounter \nOverflow\nCounterEnd Event Generated\nCounter is \nActive\nCounterStartSource= EVENT or \nSignal (Edge Base ) CounterWait \nTrigger\nCounter is incrementing\nCountermode=Active\nCounterResetSource=OFF\nCounterResetSource =Event\nCounter Reset CMD\nCounter \nCompleted\nCounterResetSource=CounterEnd(Itself)\nCounterStartSource= EVENT and Signal (Edge Base )\nCounterResetSource=Event (Itself)\n0\n \n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  67 \nExample: CounterStartSource = Line (Edge Base) Example \nCountermode=OFF\nCounter is \nIDLE\nCounterEnd Event Generated\nCounterStartSource= \nLine 1\nCounterWait \nStart\nCounter Register\nCountermode=Active\nCounter \nCompleted\nCounterResetSource =CounterEnd(Itself)\nCounterStartSource= Line (Edge Base ) Example 2\nActive\nActive\nActive\nCounterTriggerActivation= \nFalling Edge\nCounter STATUS\n0\n12\n8\n5\n10\n any Tick in \nCounterEventSource\nActive\n1\n1\nCounterDuration=12\n0\nThe Second StartSource Pulse is ignored\n \n \n \n68  •  Operational Reference \nFalcon™ 4-CLHS Series \nCycling Preset Category \nThe Cycling Preset Mode Control parameters are used to configure the camera Cycling features. Cycling controls \nallow the user to preset a number of camera operational states and then have the camera automatically switch \nbetween states in real-time, on a frame-by-frame basis. Only the features programmed to change are updated \nwhen switching between camera states, thus ensuring immediate camera response."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 150, "content": "A setup example follows the \nfeature table. Cycling Preset Mode Feature Description \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCycling Preset Mode \ncyclingPresetMode \nSets the state of the Cycling Preset Mode. 1.02 \nExpert \nDFNC \nOff \nOff \nDisable the Cycling Preset Mode feature. Active \nActive \nEnable the Cycling Preset Mode feature. Cycling Preset Count \ncyclingPresetCount \nSpecifies the number of possible Presets available. 1.02 \nExpert \nDFNC \nCycling Preset \nIncremental Source \ncyclingPresetIncrementalSource \nSpecifies the source that increments the currently active \ncycling preset."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 151, "content": "1.02 \nExpert \nDFNC \nNone \nNone \nFeature cyclingPresetCurrentActiveSet is used to select the \ncurrent active set. Valid Frame Trigger \nValidFrameTrigger \nIncrement on a Valid Frame Trigger. Counter 1 End \nCounter1End \nIncrement on the end of Counter 1. Start of Frame \nStartOfFrame \nIncrement on the Start of Frame event. Line2 \nLine2 \nSelect Line 2 (and associated I/O control block) to use as the \nexternal increment source. Falcon™ 4-CLHS Series \nOperational Reference  •  69 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nCycling Preset Repeater \ncyclingPresetRepeater \nSpecifies the required number of cycling preset increment \nevents (generated by the Cycling Preset Incremental Source) \nto increment the index of the Cycling Preset Current Active \nSet."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 152, "content": "The difference with cyclingPresetRepeater is that this \nfeature value is specific to the current cycling set specified by \ncP_PresetConfigurationSelector. 1.02 \nExpert \nDFNC \nCycling Preset Reset \nSource \ncyclingPresetResetSource \nSpecifies the source that resets the currently active cycling \npreset to the first set. 1.02 \nExpert \nDFNC \nValid Frame Trigger \nValidFrameTrigger \nReset when a Valid Frame Trigger occurs. Counter 1 End \nCounter1End \nReset when Counter 1 ends. Acquisition End \nEndOfAcquisition \nUse End of Acquisition as the reset source. An End of \nAcquisition occurs on acquisition stop."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 153, "content": "Software \nSoftware \nUse a software command as the reset source. Cycling Preset Reset \ncyclingPresetResetCmd \nReset the position of the preset cycling to 1 and the count to \n0. \n1.02 \nExpert \nDFNC \nCycling Preset Current \nActive Set  \ncyclingPresetCurrentActiveSet \nSpecifies the currently active cycling preset. 1.02 \nExpert \nDFNC \nFeatures Activation \nSelector \ncP_FeaturesActivationSelector \nSelects the feature to control by the \ncP_FeaturesActivationMode feature. 1.02 \nExpert \nDFNC \nExposure Time \nExposureTime The cP_FeaturesActivationMode feature controls the \nexposure time. Exposure Delay \nExposureDelay"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 154, "content": "The cP_FeaturesActivationMode feature controls the \nexposure delay. Output Line3 \nOutputLine3Control \nThe cP_FeaturesActivationMode feature controls output \nline3. Output Line4 \nOutputLine4Control \nThe cP_FeaturesActivationMode feature controls output \nline4. Output Line5 \nOutputLine5Control \nThe cP_FeaturesActivationMode feature controls output \nline5. Output Line6 \nOutputLine6Control \nThe cP_FeaturesActivationMode feature controls output \nline6. Multiple ROI \nMultiROI The cP_FeaturesActivationMode feature controls the ROIs. Only OffsetY is cycled, the height of each ROI is defined in \nthe Image Format category. Activate Multiple ROI Mode in \nImage Format to enable cycling MultiROI. DigitalGain DigitalGain The cP_FeaturesActivationMode feature controls the digital \ngain."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 155, "content": "Lens Shading Correction \nLensShadingCorrection The cP_FeaturesActivationMode feature controls the Lens \nShading Correction set. 1.03 \nFeatures Activation Mode \ncP_FeaturesActivationMode \nEnable the selected feature to be part of cycling presets. Note that the Multiple ROI Mode feature must already be \nActive before Multiple ROI cycling can be activated; similarly, \nthe Lens Shading Correction Mode must already be Active \nbefore Lens Shading Correction cycling can be activated. Activating any of these two features in Cycling Preset will \nautomatically set the corresponding standard camera feature \nmode to read only. 1.02 \nExpert \nDFNC \nOff \nOff \nExclude the selected feature from the cycling."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 156, "content": "Active \nActive \nInclude the selected feature in the cycling. Cycling Preset Selector \ncP_PresetConfigurationSelector \nSelects the Cycling Preset to configure its feature. 1.02 \nExpert \nDFNC \nGain \ncP_DigitalGain \nSets the selected gain as an amplification factor applied to \nthe image. This gain is applied when the current Cycling \nindex is active. 1.02 \nExpert \nDFNC \n \n \n70  •  Operational Reference \nFalcon™ 4-CLHS Series \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nExposure Time \ncP_ExposureTime \nSets the exposure time in microseconds. Applicable only \nwhen the Exposure Mode feature is set to Timed."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 157, "content": "This \nsetting will be applied when the current Cycling index occurs \n(varying exposure will have an impact on maximum \nframerate available). 1.02 \nExpert \nDFNC \nExposure Delay \ncP_ExposureDelay \nSets the exposure delay in microseconds for the selected \nset. 1.02 \nExpert \nDFNC \nROI Selector \ncP_MultiROISelector \nSelect an ROI (Region of Interest) when Multiple ROI Mode \nis enabled. Selector range is from 1 to the ROI Count \nVertical value, or maximum 8 if ROI Count Vertical is greater \nthan 8. The height of each ROI is from ROI Height on Image \nFormat page. Smaller ROI ID has smaller OffsetY.  \n1.02 \nExpert \nDFNC \nROI Offset Y \ncP_MultiROIOffsetY \nVertical offset (in pixels) from the origin to the selected ROI \n(Region of Interest)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 158, "content": "1.02 \nExpert \nDFNC \nLine Selector \ncP_LineSelector \nCycling Preset Line Selector. 1.02 \nExpert \nDFNC \nLine 3 \nLine3 \nIndex of the physical line and associated I/O control block to \nuse. Pin 6 is the Output Signal and Pin 4 is the common \noutput power on the I/O connector. Line 4 \nLine4 \nIndex of the physical line and associated I/O control block to \nuse. Pin 8 is the Output Signal and Pin 4 is the common \noutput power on the I/O connector. Line 5 \nLine5 \nIndex of the physical line and associated I/O control block to \nuse. Pin 9 is the Output Signal and Pin 4 is the common \noutput power on the I/O connector. Line 6 \nLine6 \nIndex of the physical line and associated I/O control block to \nuse. Pin 10 is the Output Signal and Pin 4 is the common \noutput power on the I/O connector. Output Line Source \ncP_OutputLineSource Selects what to output on the selected output line. 1.02 \nExpert \nDFNC \nOff \nOff \nLine output is disabled (Tri-State) or Open with Optocoupled \noutput."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 159, "content": "Pulse On: Start of \nExposure \nPulseOnStartofExposure \nGenerate a pulse on the ExposureStart event. Pulse On: End of Timer 1 \nPulseOnEndofTimer1 \nGenerate a pulse on the end of timer1. Pulse On: End of Counter \n1 \nPulseOnEndofCounter1 \nGenerate a pulse on the end of Counter1. Pulse On: Input 1 \nPulseOnInput1 \nGenerate a pulse on the Input Signal 1 event. Pulse On: Input 2 \nPulseOnInput2 \nGenerate a pulse on the Input Signal 2 event. Software Controlled \nSoftwareControlled \nThe OutputLineValue feature changes the state of the output."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 160, "content": "Pulse On: CLHS In \nPulseOnLinkTrigger0 \nGenerate a pulse on LinkTrigger0 signal. Exposure Active \nExposureActive \nGenerate the Exposure Active state on specific output. Output Line Value \ncP_OutputLineValue \nCycling Preset Output Line Value. 1.02 \nExpert \nDFNC \nActive \nActive \nActive sets the output circuit to closed. Inactive \nInactive \nInactive sets the output circuit to open. Lens Shading Set \ncP_LensShadingSet \nSets the lens shading correction set in the current cycle."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 161, "content": "1.02 \nExpert \nDFNC \nSet1 \nSet1 \nUse Lens Shading Set1 in this cycle. Set2 \nSet2 \nUse Lens Shading Set2 in this cycle. Set3 \nSet3 \nUse Lens Shading Set3 in this cycle. Set4 \nSet4 \nUse Lens Shading Set4 in this cycle. Falcon™ 4-CLHS Series \nOperational Reference  •  71 \nUsing Cycling Presets—a Simple Example \nAs presented in this category’s overview, the cycling preset features allow setting up camera configurations that \ncan change dynamically and repeatedly, with minimum overhead. The features that change, along with the trigger \nfor the feature change are preprogrammed in the camera."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 162, "content": "Additionally, a set of preset features can be updated \nwhile the camera is acquiring with a different preset. Such dynamic feature changes allow applications to perform \ntracking algorithms. The following example describes a simple cycling sequence (using free running acquisitions) with exposure \nchange steps that will repeat until stopped by the user. This example uses the Sapera CamExpert tool to set \nfeatures and test the sequence. Multi-Exposure Cycling Example Setup  \nIn the Camera Control category, set the following features as follows: \n• \nAcquisition Frame Rate: 4.0  \n• \nExposure Time: 1000 µs (somewhat short). In the Cycling Preset category, set the following features as follows: \n• \nCycling Preset Mode: Active."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 163, "content": "This feature enables the Cycling Preset Module. • \nCycling Preset Count: 4. This represents the number of presets which will be configured and used. • \nCycling Preset Incremental Source: Start Of Frame. This event will be used to increment the cycling preset \nindex, and is a logical choice in a free-running acquisition setup. • \nCycling Preset Repeater: 4. This represents the number of incremental source events to count before \nswitching to the next preset. In this example we are counting Start Of Frame events, thus a value of 4 (with a \ntest setup of 4 fps) will switch preset every 1 second. • \n(Optional) Cycling Preset Reset Source: Acquisition End. This feature defines the event that will reset the \npreset index back to 1. In this example, setting the feature to Acquisition End returns the cycling preset index \nto the start (1) when the Acquisition Stop feature is clicked in CamExpert to stop the acquisition. • \nFeatures Activation Selector: Exposure Time."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 164, "content": "• \nFeatures Activation Mode: Active. The Exposure Time field now controls the camera exposure time. The \nprimary exposure time field in the Camera Control Category is now indicating a read only field. • \nCycling Preset Selector: 1. \n• \nExposure Time: leave as is. The feature shows the last exposure time used by the camera (before cycling \nwas enabled, namely 1000 µs). • \nCycling Preset Selector: 2. \n• \nExposure Time: higher value than the previous setting."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 165, "content": "This will increase acquisition brightness. • \nRepeat for Cycling Preset Selector 3 and 4, each with a longer exposure. Test the Example \n• \nWith 4 different exposure times saved in four presets, click the CamExpert Grab button to start the cycling \nfree-running acquisition. • \nThe CamExpert live display window will show a live grab of 4 fps, where each second shows a step increase \nin exposure, which then returns to the first exposure after 4 seconds, cycling continuously until stopped by the \nuser. 72  •  Operational Reference \nFalcon™ 4-CLHS Series \nCycling Reset Timing Details This section describes the cycling function with two cycling feature configurations."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 166, "content": "These configurations (or cases) \nare dependent on the cycling preset increment source as follows: \n• \nInternal Synchronous Increment: the preset increment source is either a Start of Frame or Valid Frame \nTrigger event (cyclingPresetIncrementalSource = StartOfFrame or ValidFrameTrigger). • \nExternal Asynchronous Increment: the preset increment source is either a Counter 1 End event, a Line2 \nsignal, or None (cyclingPresetIncrementalSource = Counter1End or Line2 or cyclingPresetCurrentActiveSet). Case 1: Cycling with Internal Synchronous Increment \nWith an Internal Synchronous Cycling Increment, a cycling reset command will execute on the next cycling \nincrement event. Preset 1 (cycling status) Preset 2\nPreset 3\nPreset 1\nAcquisition \nCommand\nFrame Acquisition 1\nIncrement Source\ncyclingPresetIncrementalSource\nFrame Acquisition 2\nIncrement Source\ncyclingPresetIncrementalSource\nFrame Acquisition 3\nIncrement Source\ncyclingPresetIncrementalSource\nFrame Acquisition 4\nIncrement Source\ncyclingPresetIncrementalSource\nAsynchronous Cycling Reset\ncyclingPresetResetSource\nReset Applied\ncyclingPresetCurrentActiveSet\n \nCase 2: Cycling with External Asynchronous Increment \nWith an External Asynchronous Cycling Increment, a cycling reset command executes immediately and sets the \ncycling preset to set number 1. Preset 1 (cycling status)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 167, "content": "Preset 2\nPreset 3\nPreset 1\nAcquisition \nCommand\nFrame Acquisition 1\nIncrement Source\ncyclingPresetIncrementalSource\nFrame Acquisition 2\nIncrement Source\ncyclingPresetIncrementalSource\nFrame Acquisition 3\nFrame Acquisition 4\nIncrement Source\ncyclingPresetIncrementalSource\nAsynchronous Cycling Reset Applied\ncyclingPresetResetSource\ncyclingPresetCurrentActiveSet\nPreset 2\nPreset 3\n \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  73 \nUsing Cycling Presets with Output Controls \nThe following graphic shows a Cycling Preset function setup where a two-stage setup performs exposures of \ndifferent length and additionally provides an output pulse at the start of each exposure. As an example, by using both output lines, this setup can trigger two separate light strobes of different \nwavelengths. This dual exposure sequence example is controlled by a single external trigger. Feature Settings for this Example \nBelow are listed key features for this setup. Other features will be as required by the user. • \nI/O Controls: \n• \nTriggerSelector = FrameBurstStart \n• \nTriggerMode = On \n• \ntriggerFrameCount = 2 \n• \nCycling Preset \n• \ncyclingPresetMode = Active \n• \ncyclingPresetCount = 2 \n• \ncyclingPresetIncrementalSource = StartOfFrame \n• \ncP_FeaturesActivationSelector = ExposureTime  \n• \ncP_FeaturesActivationMode = Active (plus set required exposure for each cycling preset) \n• \ncP_LineSelector = Line3 (for preset 1) and Line4 (for preset 2) \n• \ncP_OutputLineSource = PulseOnStartofExposure (line3–preset 1, line4–preset 2)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 168, "content": "Acquisition 1 Exposure\nReadout 1\nAcquisition 2 Exposure\nReadout 2\nExternal Trigger\nOutput 1 (Line 3)\nOutput 2 (Line 4)\nPulseOnStartofExposure\nPulseOnStartofExposure\n \n \n \n \n74  •  Operational Reference \nFalcon™ 4-CLHS Series \nCycling Presets with Multiple ROIs \nThe Cycling Preset Mode feature supports changing ROIs from one preset to the next. An ROI in this case refers \nto a single acquisition area which is a subset of the complete image frame. • The initial position and size of the ROIs (i.e., features Width, ROI Offset Y, ROI Height) are setup via the \nImage Format category. Only the ROI Offset Y can be changed between presets. • \nThe number of ROIs to be cycled is defined by the ROI Count Vertical value in the Image Format category, up \nto a maximum of 8."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 169, "content": "• \nThe Multiple ROI Mode in the Image Format category must be set to Active to use cycling presets with \nMultiple ROI (Features Activation Selector = Multiple ROI). It will become unavailable when the Features \nActivation Mode is set to Active and will be made available again only when the Features Activation Mode is \nset to Off. • \nDuring cycling, the image width, the height of each ROI, and the number ROIs remain same (Width, ROI \nHeight, and ROI Count Vertical, respectively). The only feature that may change from one preset to the next is \nthe ROI Offset Y feature of each ROI. To do so, the following features should be set as indicated: Category \nFeature \nValue \nImage Format \nMultiple ROI Mode \nActive \nCycling Preset  \nFeatures Activation Selector \nMultiple ROI \nCycling Preset \nFeatures Activation Mode \nActive \nCycling Preset \nCycling Preset Mode \nOff \n \n• \nROIs cannot overlap in a preset."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 170, "content": "If a change causes an overlap to occur, the ROI Offset Y value will be \nautomatically adjusted to eliminate it. In the example below, modifying the Offset Y of ROI 2 causes an \noverlap with ROI 3; the Offset Y value of ROI 3 is adjusted to eliminate the overlap. Falcon™ 4-CLHS Series \nOperational Reference  •  75 \nCycling Presets with Lens Shading Correction \nThe Cycling Preset Mode feature supports changing Lens Shading Correction from one preset to the next. • \nThe Lens Shading Correction Mode (Data Processing > Lens Shading Correction category) must be set to \nActive to activate the feature in Cycling Preset. If it is Off, the activation of the feature in Cycling Preset will be \nunavailable. Data Processing > Lens Shading Correction category \nCycling Preset category \n \n \n \n \n \n• \nWhile the Features Activation Mode is set to Active in Cycling Preset, the Lens Shading Correction Mode \ncannot be deactivated."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 171, "content": "Cycling Preset category \nData Processing > Lens Shading Correction category \n \n \n \n• \nThe Features Activation Mode for Lens Shading Correction and for Multiple ROI cannot be both Active at the \nsame time (they cannot both be cycled). • \nWhen the Features Activation Mode for Lens Shading Correction is Active, the Multiple ROI Mode can be \nActive, but ROIs remain the same on all presets. Furthermore, cycling with Lens Shading Correction only \nsupports up to 8 ROIs: if more are defined, only the first 8 are used. • \nWhen a User Shading Coefficients set is not saved or reset, it does not show up as an option in Lens Shading \nSet under Cycling Preset Selector. • \nLens Shading Correction Mode can be set to Calibration only when both Cycling Preset Mode and Features \nActivation Mode for Lens Shading Correction are Off. See Lens Shading Calibration."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 172, "content": "76  •  Operational Reference \nFalcon™ 4-CLHS Series \nMetadata Controls Category \nThe Metadata Controls category groups parameters used to configure the inclusion of various metadata (referred \nto as chunk data in GenApi) in the payload of the image. Note that metadata is only available in 8-bit mode. Also, if an ROI is defined, the horizontal ROI must be at least \n416 columns (pixels) wide. Metadata Controls Feature Description \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nMetadata Mode \nChunkModeActive \nActivates the inclusion of chunk data in the payload of the \nimage. 1.02 \nGuru \n \n \nFalcon™ 4-CLHS Series \nOperational Reference  •  77 \nExtracting Metadata Stored in a Sapera Buffer \nThe metadata location is always the beginning of the last line of the image. Metadata is ordered as follows: \n \nThe Chunk ID, Chunk Size and Chunk Availability fields are always present in the metadata; all the other fields \nare optional."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 173, "content": "Metadata Structure The metadata currently contains the following values, in this order. Type \nValue \nDescription \nAvailable Bit \nunsigned int \nChunk ID The Chunk ID is hardcoded to 0xCD000002 when \npixels are read in reverse order (3 – 0). Always available. unsigned int \nChunk Size \nThe Chunk Size is currently 400. This value is \nhardcoded in pixel 7. Always available. unsigned long long \nChunk Availability \nDetermines the availability of metadata. For bits [1:31]: \n0: Metadata not available. 1: Metadata available. Refer to the Metadata Availability section for \ninformation on the bit field structure."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 174, "content": "Always available  \n(bit 0 not used) \nunsigned long long \nExposure Time \nExposure time, in µs. \n1 \nunsigned long long \nCycling Current Set \nCycling current set. 2 \nChunk ID\nChunk Size\nChunk Availability\nExposure Time\nCycling Current Set\nLines Status All\nAnalog Gain\nDigital Gain\nOffset X\nOffset Y\nCounter Value at Reset\nSize X\nSize Y\nReserved\nTimeStamp\nBinning\nTest Image Selector\nDevice ID\nDevice User ID\nPixel Format\nExposure Delay\nNumber of ROI\nPixel Number\n0\n4\n16\n24\n32\n40\n48\n56 58 60\n64 66 68\n72\n80\n88\n96\n112\n128\n136\n144\n152\n4\n4\n8\n8\n8\n8\n8\n8\n2 2\n2 2\n4\n4\n8\n8\n8\n16\n16\n8\n8\n8\n    \n160\n408\n...\nROI1 Offset X\nROI1 Offset Y\n2\n2\nROI1 Size X\nROI1 Size Y\n2 2\nROI2 Offset X\nROI2 Offset Y\n2\n2\nROI2 Size X\nROI2 Size Y\n2 2\nROI32 Offset X\nROI32 Offset Y\n2\n2\nROI32 Size X\nROI32 Size Y\n2 2\n \n \n78  •  Operational Reference \nFalcon™ 4-CLHS Series \nType \nValue \nDescription \nAvailable Bit \nunsigned long long \nLine Status All \nThe line status for available lines is represented as a \nbitfield, with 0 = low and 1 = high. bit0: GPIO \nbit1: GPI1 \nbit2: GPO0 \nbit3: GPO1 \nbit4: GPO2 \nbit5: GPO3 \nbit6: GPO4 \nbit7: CLHS Trigger \n6 \nunsigned long long \nAnalog Gain \nAnalog gain. Values returned in units of 0.1 gain. For \nexample, 30 = 3.0. \n8 \nunsigned long long \nDigital Gain \nDigital gain, as raw gain. 4 \nunsigned long long \nFactory Gain \nFactory gain, as raw gain."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 175, "content": "4 \nunsigned short \nOffset X \nOffset X, in pixels. 10 \nunsigned short \nOffset Y \nOffset Y, in pixels \nunsigned int \nCounter Value at Reset \nCounter1 value at reset. 16 \nunsigned short \nWidth \nImage width (size X), in pixels. 11 \nunsigned short \nHeight \nImage height (size Y), in pixels. unsigned int \nReserved \nNot used. unsigned long long \nTimestamp \nTimestamp (in microseconds) of the start of the frame’s \nacquisition, as identified by the camera’s internal \nTimestamp clock."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 176, "content": "12 \nunsigned long long \nBinning \nBinning is coded using the first byte (pixel 80):  \nBits [0:3]: binning horizontal \nBits [4:7]: binning vertical  \nFor example, no binning is indicated by 1 in horizontal \nand 1 in vertical: \n0001 0001 (decimal = 17) \n14 \nunsigned long long \nTest Image Selector \nTest pattern image. Possible values are: \n255: Off \n0: GreyHorizontalRamp \n1: GreyVerticalRamp \n9: GreyDiagonalRampMoving \n28 \nunsigned char [16] \nSerial Number \nASCII codes for device serial number. 29 \nunsigned char [16] \nDevice User ID \nASCII codes for Device User ID. \n30 \nunsigned long long \nPixel Format \nPixel format. Possible values are: \n0x01080001: Mono8 \n0x01100003: Mono10 \n31 \nunsigned long long \nExposure Delay \nExposure delay."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 177, "content": "If not used, the default value 10 μs is \nreturned. 15 \nunsigned int \nReserved \nNot used. unsigned int \nReserved \nNot used. unsigned long long \nNumber of ROIs \nThe total number or ROIs \n27 \nunsigned short \nROI1 Offset X \nROI1 Offset X, in pixels. unsigned short \nROI1 Offset Y \nROI1 Offset Y, in pixels. unsigned short \nROI1 Size X \nROI1 Size X, in pixels. unsigned short \nROI1 Size Y \nROI1 Size Y, in pixels. unsigned short \n… \n… \n \nunsigned short \nROI32 Offset X \nROI32 Offset X, in pixels. unsigned short \nROI32 Offset Y \nROI32 Offset Y, in pixels. unsigned short \nROI32 Size X \nROI32 Size X, in pixels. unsigned short \nROI32 Size Y \nROI32 Size Y, in pixels. Falcon™ 4-CLHS Series \nOperational Reference  •  79 \nDigital Gain (raw) \nDigital gain is expressed in the metadata in its raw format."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 178, "content": "To convert from this raw format to an actual gain value \nuse this formula:   \n \n𝐺𝑎𝑖𝑛=\nDigital Gain (raw)\nFactory Gain (raw)  \n \nExample: a digital gain raw value of 4402 and a factory gain raw value of 2201 would translate to an actual gain \nof 2. Analog Gain (raw) \nAnalog gain is expressed in the metadata in its raw format. To convert from this raw format to an actual gain \nvalue use this formula: \n   \n𝐴𝑛𝑎𝑙𝑜𝑔 𝐺𝑎𝑖𝑛=\nAnalog Gain (raw)\n10\n  \n \nExample: an analog gain raw value of 20 would translate to an actual gain of 2. 80  •  Operational Reference \nFalcon™ 4-CLHS Series \nFile Access Control Category \nThe File Access control in CamExpert allows the user to quickly upload various data files to the connected \nFalcon4 device. The supported data files are for firmware updates and other types. File Access Control Feature Descriptions \n \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nFile Selector \nFileSelector \nList of device files."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 179, "content": "1.00 \nGuru \nAll Firmware \nFirmware1 \nAll Firmware - FPGA code, microcode, XML, and Start-up \nScript. Factory settings \nUserSet0 \nSet the camera settings to factory defaults. User settings 1 \nUserSet1 \nUser settings set 1. User settings 2 \nUserSet2 \nUser settings set 2. Last Saved Image \nSavedImage \nLast saved image. Lens Shading Correction \nLensShadingCorrection0 \nLens Shading coefficients set."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 180, "content": "The set is defined in Current \nLens Shading Set. User Shading Coefficients 1 \nShadingCoefficients1 \nLens Shading coefficients set 1. User Shading Coefficients 2 \nShadingCoefficients2 \nLens Shading coefficients set 2. \n1.03 \nUser Shading Coefficients 3 \nShadingCoefficients3 \nLens Shading coefficients set 3. \n1.03 \nUser Shading Coefficients 4 \nShadingCoefficients4 \nLens Shading coefficients set 4. \n1.03 \nFactory Defective Pixel Map \nBadPixelCoordinate0 \nSelect the Factory Defective Pixel Map. User Defective Pixel Map \nBadPixelCoordinate1 \nSelect the User Defective Pixel Map. LUT \nLUT \nLUT. Falcon™ 4-CLHS Series \nOperational Reference  •  81 \nDisplay Name \nFeature & Values \nDescription \nDevice \nVersion \n& View \nFile Operation Execute \nFileOperationExecute \nExecutes the operation selected by File Operation Selector \non the selected file."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 181, "content": "1.00 \nGuru \nFile Open Mode \nFileOpenMode \nSelects the access mode used to open a file on the device. 1.00 \nGuru \nRead \nRead \nSelect READ only open mode. Write \nWrite \nSelect WRITE only open mode. File Access Offset \nFileAccessOffset \nControls the file Offset where the read or write operation will \nbe perform. 1.00 \nGuru \nFile Access Length \nFileAccessLength The number of bytes to transfer between the file and the \nFileAccessBuffer."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 182, "content": "1.00 \nGuru \nFile Access Buffer \nFileAccessBuffer \nDefines the intermediate access buffer that allows the \nexchange of data between the device file storage and the \napplication. 1.00 \nBeginner \nFile Size \nFileSize \nRepresents the size of the selected file in bytes. 1.00 \nBeginner \nFile Operation Selector \nFileOperationSelector \nSelects the target operation for the selected file in the \ndevice. 1.00 \nGuru \nOpen \nOpen \nSelect the Open operation – executed by \nFileOperationExecute. Close \nClose \nSelect the Close operation – executed by \nFileOperationExecute \n \nRead \nRead \nSelect the Read operation – executed by \nFileOperationExecute. Write \nWrite \nSelect the Write operation – executed by \nFileOperationExecute."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 183, "content": "File Operation Status \nFileOperationStatus \nReturns the status of the last file operation. 1.00 \nGuru \nSuccess \nSuccess \nThe last file operation has completed successfully. Invalid Parameter \nInvalidParameter The last file operation has completed unsuccessfully \nbecause of an invalid parameter. Write Protect \nWriteProtect The last file operation has completed unsuccessfully \nbecause the file is read-only. File Invalid \nFileInvalid The last file operation has completed unsuccessfully \nbecause the selected file in not present in this camera \nmodel. File Not Open \nFileNotOpen The last file operation has completed unsuccessfully \nbecause the selected file has not been opened. File Too Big \nFileTooBig \nThe last file operation has completed unsuccessfully \nbecause the file is larger than expected. File Operation Result \nFileOperationResult"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 184, "content": "The number of successfully read/written bytes during the \nlast operation. 1.00 \nBeginner \nSave Last Image to Flash \nsaveLastImageToFlash \nCommand that saves the last acquired image to camera \nflash memory. Use the file transfer feature to read the image \nfrom camera. Invisible \n \n \n \n \n \n \n82  •  Operational Reference \nFalcon™ 4-CLHS Series \nUpdating Firmware via File Access in CamExpert \n1. Click Setting next to the Upload/Download File parameter. The File Access Control dialog opens. 2. From the Type list, select the file type to upload to the camera, in this case Device Firmware. 3. From the File Selector list, select All Firmware. 4. Click Browse to select the specific file from the system drive or from a network location. 5."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 185, "content": "Click Upload (to Camera) to transfer the file to the camera. 6. Reset the Falcon4 when prompted. Falcon™ 4-CLHS Series \nImplementing Trigger-to-Image Reliability  •  83 \nImplementing Trigger-to-Image \nReliability \nOverview \nIn a complex imaging system, a lot can go wrong at all points – from initial acquisition, camera processing, to data \ntransmission. Teledyne DALSA provides features, events, and I/O signals that provide the system designer with \nthe tools to qualify the system in real time. The Teledyne DALSA website provides general information, FAQ, and White Paper downloads about the Trigger-\nto-Image Reliability (T2IR) framework in hardware and Sapera LT software SDK. See \nhttps://www.teledynedalsa.com/en/learn/knowledge-center/trigger-to-image-reliability-t2ir/. \nT2IR with Falcon4-CLHS \nBenefits for imaging systems include:  \n• \nMakes system more predictable \n• \nPrevents many errors before they happen \n• \nManages system exceptions in controlled manner \n• \nProvides system debugging and tracing \n• \nReduces downtime \nThe Falcon4 provides a number of features for system monitoring: \n• \nBuilt-in Self-Test on power-up and reset after firmware change \n• \nInternal temperature reporting \n• \nIn-camera event status flags  \n• \nInvalid external trigger \n• \nImage lost  \n \n \n84  •  Implementing Trigger-to-Image Reliability \nFalcon™ 4-CLHS Series \nFeatures for T2IR Monitoring \nThe following table presents some of the camera features a developer can use for T2IR monitoring."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 186, "content": "The output \nline signals would interface to other external devices. Camera Status Monitoring \nRefresh BIST \ndeviceBIST \nDevice Built-In Self Test Status \ndeviceBISTStatus \nDevice Version \nDeviceVersion \nFirmware Version \nDeviceFirmwareVersion \nManufacturer Part Number \ndeviceManufacturerPartNumber \nManufacturer Info \nDeviceManufacturerInfo \nAcquisition and Triggers \nValid Frame Trigger \nValidFrameTrigger \nInvalid Frame Trigger \nInvalidFrameTrigger \n \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  85 \nTechnical Specifications \nBoth 2D and 3D design drawings are available for download from the Teledyne DALSA web site [https://www.teledynedalsa.com/en/products/imaging/cameras/falcon4-clhs/]. Falcon4-CLHS Identification and Mechanical Notes \nIdentification Label \n \nNOTE \nFalcon4 cameras have an identification label applied to the bottom side, with the following information: Model Part Number \nSerial Number \n2D Barcode \n \nAdditional Mechanical Notes  \n \nNOTE For information on lens requirements see Choosing a Lens with the Correct Image Circle."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 187, "content": "Each camera side has two mounting holes in identical locations, which provide good grounding capabilities. Overall height or width tolerance is ± 0.05 mm. Temperature Management  \nFalcon4-CLHS cameras are designed to optimally transfer internal component heat to the outer metallic body. If \nthe camera is free standing (that is, not mounted) it will be hot to the touch. Basic heat management is achieved by mounting the camera onto a metal structure via its mounting screw holes. Heat dissipation is improved by using thermal paste between the camera body (not the front plate) and the metal \nstructure plus the addition of a heatsink structure."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 188, "content": "86  •  Technical Specifications \nFalcon™ 4-CLHS Series \nMechanical Specifications with M42 Mount \n \nFalcon4 11M, models M2240, M4400 and M4480. Falcon™ 4-CLHS Series \nTechnical Specifications  •  87 \n \n \nFalcon4 37M/67M, models M6200 and M8200. 88  •  Technical Specifications \nFalcon™ 4-CLHS Series \nSensor Alignment Specification The following figure specifies sensor alignment for Falcon4 where all specifications define the absolute maximum \ntolerance allowed for production cameras. Dimensions X, Y, Z, are in microns and referenced to the Falcon4 \nmechanical body or the optical focal plane (for the Z-axis dimension). Theta specifies the sensor rotation relative \nto the sensor’s center and Falcon4 mechanical."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 189, "content": "X variance \n+/- 100 microns \n(+/-) Y variance\n(+/-)  X variance\nSensor Alignment Reference\nZ variance not shown\n(+/-)  theta variance\n \nY variance \n+/- 100 microns \nZ variance \n+/- 300 microns \nTheta variance \n+/- 1 degree \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  89 \nConnectors \nThe Falcon4-CLHS camera has two connectors on its back panel. There is one CLHS (CX4) standard data and \ncontrol connector plus a locking 10-pin connector for power and I/O signals. These are described below. Camera Link HS (CX4) \nThe Camera Link HS camera connector (CX4) is defined in document Specifications of the Camera Link HS \nInterface Standard for Digital Cameras and Frame Grabbers, which is at version 1.1 at the time of this \nmanual’s writing. Typically, there is no need to be concerned with the physical pinout of the CX4 connector or \ncables."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 190, "content": "Refer to the site www.automate.org for additional information. 10-pin I/O Connector Details \nA DC power source is connected to the 10-pin connector (SAMTEC TFM-105-02-L-D-WT). Falcon4-CLHS \nsupports connecting cables with retention latches and/or screw locks. The following figure shows the pin number \nassignment. Teledyne DALSA makes available optional I/O cables as described in I/O Cable Accessories. Contact Sales for \navailability and pricing."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 191, "content": "90  •  Technical Specifications \nFalcon™ 4-CLHS Series \nPinout Details for FA-HM00-M4485 \n \nPin Number \nSignal \nDirection \nDefinition \n1 \nPWR-GND \n— \nCamera Power – Ground \n2 \nPWR-VCC \n— \nCamera Power – DC +10 to +30 Volts  \n3 \nGPI-Common \n— \nGeneral Input/Output Common Ground \n4 \nGPO-Power \n— \nGeneral Output Common Power \n5 \nGPI 1 In \nGeneral External Input 1 \n6 \nGPO 1 \nOut \nGeneral External Output 1 \n7 \nGPI 2 In \nGeneral External Input 2 \n8 \nGPO 2 \nOut \nGeneral External Output 2 \n9 \nGPO 3 \nOut \nGeneral External Output 3 / Fast Switching Output \n10 \nReserved \n \nDo not use. Pinout Details for FA-HM10-M2245, FA-HM11-M4405, FA-HM10-M4485,  \nFA-HM10-M6205, FA-HM10-M8205  \n \nPin Number \nSignal \nDirection \nDefinition \n1 \nPWR-GND \n— \nCamera Power – Ground \n2 \nPWR-VCC \n— \nCamera Power – DC +10 to +30 Volts  \n3 \nGPI-Common \n— \nGeneral Input/Output Common Ground \n4 \nGPO-Power \n— \nGeneral Output Common Power \n5 \nGPI 1 In \nGeneral External Input 1 \n6 \nGPO 1 \nOut \nGeneral External Output 1 / Fast Switching Output \n7 \nGPI 2 \nIn \nGeneral External Input 2 \n8 \nGPO 2 \nOut \nGeneral External Output 2 / Fast Switching Output \n9 \nGPO 3 \nOut \nGeneral External Output 3 / Fast Switching Output \n10 \nGPO 4 \nOut \nGeneral External Output 4 / Fast Switching Output \n \nCamera DC Power Characteristics \n \nDC Operating Characteristics \nM2240, M4400 and M4480 \nM6200, M8200 \nInput Voltage  \n+10 V minimum \n+10 V minimum \nInput Power Consumption @ +12 V Supply \n10.02 W typical \n14.8 W typical \nInput Power Consumption @ +24 V Supply \n9.6 W typical \n14.1 W typical \n \n \n \nAbsolute Maximum DC Power Supply Range before Possible Device Failure \nInput Voltage  \n–50 V DC \n+50 V DC \n \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  91 I/O Mating Connector Specifications & Sources"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 192, "content": "For users wishing to build their own custom I/O cabling, the following product information is provided to expedite \nyour cable solutions. The SAMTEC web information for the discrete connector and a cable assembly with \nretention clips follows the table below. MFG \nPart # \nDescription \nData Sheet \nSamtec \nISDF-05-D \nISDF-05-D-M (see image below) Discrete Connector \n(see example below) \nhttps://www.samtec.com/products/isdf \nSamtec \nSFSD-05-[WG]-G-[AL]-DR-[E2O] WG : Wire Gauge \nAL : Assembled Length \nE2O : End 2 Option \nDiscrete Cable \nAssembly \n(see example below) \nhttps://www.samtec.com/products/sfsd \nISDF-05-D-M Connector Availability On-Line \nNorth-America (specific country can be selected) \nhttp://www.newark.com/samtec/isdf-05-d-m/connector-housing-receptacle-\n10/dp/06R6184 \nEurope (specific country can be selected)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 193, "content": "http://uk.farnell.com/samtec/isdf-05-d-m/receptacle-1-27mm-crimp-\n10way/dp/2308547?ost=ISDF-05-D-M \nAsia-Pacific (specific country can be selected) \nhttp://sg.element14.com/samtec/isdf-05-d-m/receptacle-1-27mm-crimp-\n10way/dp/2308547?ost=ISDF-05-D-M \nImportant: Samtec ISDF-05-D-S is not compatible with Falcon4. Samtec ISDF-05-D-M mating connector for customer-built cables w/retention clips \n“.050” Tiger Eye™ Discrete Wire Socket Housing” \n \n \n \n \n92  •  Technical Specifications \nFalcon™ 4-CLHS Series \nSamtec connector-cable assembly SFSD-05-28-H-03.00-SR w/retention clips \n“.050” Tiger Eye™ Double Row Discrete Wire Cable Assembly, Socket” \n \n \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  93 \nInput Signals Electrical Specifications \nExternal Inputs Block Diagram \nProtection\nCurrent \nLimiter\nProtection\nCurrent \nLimiter\nInput 2 (pin 7) Input 1 (pin 5)\nCommon Ground (pin 3) External Input Details \n• \nOpto-coupled with internal current limit. • \nSingle input trigger threshold level  \n(TTL standard: <0.8 V = Logical LOW, >2.4 V = Logical HIGH. See lineDetectionLevel feature)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 194, "content": "• \nUsed as trigger acquisition event, counter or timestamp event, or integration control. • \nUser programmable debounce time from 0 to 255 µs in 1 µs steps. • \nSource signal requirements:  \n• \nSingle-ended driver meeting TTL, 12 V, or 24 V standards (see table below) \n• If using a differential signal driver, only one input can be used due to the shared input common (see \ndetails below) \nExternal Input DC Characteristics \n \nOperating Specification \nMinimum \nMaximum \nInput Voltage \n+3 V \n+36 V \nInput Current \n7 mA \n11.8 mA \nInput logic Low \n \n0.8 V \nInput logic High \n2.5 V \n \n \nAbsolute Maximum Range before Possible Device Failure \n \nAbsolute Ratings \nMinimum \nMaximum \nInput Voltage \n–36 V \n+36 V \n \n \n \n94  •  Technical Specifications \nFalcon™ 4-CLHS Series \nExternal Input AC Timing Characteristics \n \nConditions \nDescription \nMin \nUnit \nInput Pulse 0V – 3V  \nInput Pulse width High  \n132 \nµs \nInput Pulse width Low \n1.22 \nµs \nMax Frequency \n392 \nkHz \nInput Pulse 0V – 5V \nInput Pulse width High  \n202 \nµs \nInput Pulse width Low \n1.28 \nµs \nMax Frequency \n392 \nkHz \nInput Pulse 0V -12V \nInput"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 195, "content": "Pulse width High  \n345 \nµs \nInput Pulse width Low \n1.28 \nµs \nMax Frequency \n392 \nkHz \nInput Pulse 0V – 24V \nInput Pulse width High  \n132 \nµs \nInput Pulse width Low \n1.22 \nµs \nMax Frequency \n392 \nkHz \n \nExternal Inputs: Using TTL/LVTTL Drivers \n• \nExternal Input maximum current is limited by the Falcon4 circuits to a maximum of 12 mA. \nUser IO\nGround\nUser IO\nPower\nImax = 10mA\nImax = 10mA\nLVTTL / TTL \nPush-Pull \nBuffer\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera IO \nInterface\nExternal Signal 2\nExternal Signal 1\n(Common Ground) ( Input 1 )\n( Input 2 )\n \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  95 \nExternal Inputs: Using Common Collector NPN Drivers \n• \nExternal Input maximum current is limited by the Falcon4 circuits to a maximum of 12 mA. \nUser IO\nGround\nUser IO\nPower\n(3V-28V) Imax = 10mA\nImax = 10mA\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera IO \nInterface\nB\nE\nC\nB\nE\nC\nExternal Signal 2\nExternal Signal 1\n( Common Ground )\n( Input 1 )\n( Input 2 )\n \nExternal Inputs: Using Common Emitter NPN Driver \n• \nExternal Input maximum current is limited by the Falcon4 circuits to a maximum of 12 mA. \n• \nWarning: Only one External Signal can be used (input 1 or input 2). User IO\nGround\nUser IO\nPower\n(3V-28V)\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera IO \nInterface\nB\nE\nC\nImax = \n10mA\nOnly one Input can be used \nin this configuration.\n! External Signal\n( Common Ground )\n( Input 1 )\n( Input 2 )\n \n \n \n96  •  Technical Specifications \nFalcon™ 4-CLHS Series \nExternal Inputs: Using a Balanced Driver \n• \nWarning: Only one External Signal can be used (input 1 or input 2)."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 196, "content": "RS-422 \nCompatible \nTransmitter\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera IO \nInterface\nOnly one Input can be used \nin this configuration.\n! External Signal\n( Common Ground )\n( Input 1 )\n( Input 2 ) Falcon™ 4-CLHS Series \nTechnical Specifications  •  97 \nOutput Signals Electrical Specifications \nExternal Outputs Block Diagram \nProtection\nCurrent \nLimiter\nProtection\nCurrent \nLimiter\nOutput 2 (pin 8)\nOutput 1 (pin 6) Output Common Power \n(pin 4)\n \nExternal Output Details and DC Characteristics \n• \nProgrammable output mode such as strobe, event notification, etc. (see outputLineSource feature) \n• \nOutputs are open on power-up with the default factory settings  \n• \nA software reset will not reset the outputs to the open state if the outputs are closed  \n• \nA user setup configured to load on boot will not reset the outputs to the open state if the outputs are closed   \n• \nNo output signal glitch on power-up or polarity reversal  \n• \nTypical Operating Common Power Voltage Range: +3 V to 28 Vdc at 24 mA \n• \nMaximum Common Power Voltage Range : ±30 Vdc \n• \nMaximum Output Current: 36 mA  \n \n \n98  •  Technical Specifications \nFalcon™ 4-CLHS Series \nExternal Output AC Timing Characteristics The graphic below defines the test conditions used to measure the Falcon4 external output AC characteristics, as \ndetailed in the tables that follows."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 197, "content": "t\nt\nOutput Control Signal\nOutput\n100%\n90%\n10%\ntd1\ntris e\ntfall\ntd2\nOutput\nOutput Common Power\nRLoad\nControl \nSignal\nCamera\n \nOutput Characteristics, FA-HM00-M4485 \nOpto-coupled Output: AC Characteristics \nNote: All measurements subject to some rounding. The following tables describes GPO 1 and GPO 2 when the load is connected to a user-provided ground. Test \nconditions are with front plate temperature ~60C. \n \nOutput Common \nPower \nOutput \nRload \ntd1 (µs) \ntrise (µs) \ntd2 (µs) \ntfall (µs) \nVout (V) \nCurrent \nTest (ohm) Leading \nDelay \nRise Time \nTrailing \nDelay \nFall Time \n3V \n8 mA \n240 \n0.459 \n3 \n11 \n20.41 \n2.17 \n12ma \n144 \n0.6 \n6.95 \n4.4 \n20 \n1.75 \n16 mA \n40 \n0.6 \n11 \n1 \n12.9 \n0.559 \n5V \n8 mA \n523 \n0.469 \n2.64 \n12 \n22 \n4.24 \n16 mA \n159 \n0.485 \n7.52 \n2.55 \n12 \n2.57 \n24 mA \n69 \n0.64 \n7.52 \n1 \n8.42 \n1.69 \n12V \n8 mA \n1400 \n0.52 \n3.28 \n10.6 \n25.64 \n11.23 \n16 mA \n595 \n0.52 \n3.28 \n4.12 \n13.86 \n9.61 \n24 mA \n360 \n0.531 \n3.76 \n2.48 \n13.8 \n8.72 \n24V \n8 mA \n2907 \n0.541 \n1.63 \n22.8 \n37.8 \n23.31 \n16 mA \n1346 \n0.556 \n2.2 \n7.4 \n18.32 \n21.58 \n24 mA \n861 \n0.567 \n2.5 \n6.61 \n12.93 \n20.72 \n \nGeneral Purpose Output 3 Fast Switching  \nGPO 3 supports a fast switching mode with ground of the user load connected to pin 3 (General Input/Output \nCommon Ground). Note, GPO 1 and GPO 2 do not support fast switching. Test conditions are with front plate \ntemperature ~60C. \n \n \n \nFalcon™ 4-CLHS Series \nTechnical Specifications  •  99 \nOutput Common \nPower \nOutput \nRload \ntd1 (us) \ntrise (µs) \ntd2 (µs) \ntfall (µs) \nVout (V) \nCurrent \nTest (ohm)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 198, "content": "Leading \nDelay \nRise Time \nTrailing \nDelay \nFall Time \n5V \n8 mA \n561 \n1 \n0.7 \n3.64 \n0.5 \n4.53 \n16 mA \n277 \n1 \n0.7 \n3.48 \n0.659 \n4.45 \n24 mA \n182 \n1 \n0.7 \n3.32 \n0.65 \n4.37 \n12V \n8 mA \n1444 \n0.934 \n0.2321 \n2.88 \n0.949 \n11.49 \n16 mA \n713 \n0.945 \n0.2563 \n2.86 \n0.42 \n11.41 \n24 mA \n467 \n0.952 \n0.2739 \n2.78 \n0.224 \n11.33 \n24V \n8 mA \n2930 \n0.81 \n0.2079 \n3.542 \n1.639 \n23.57 \n16 mA \n1464 \n0.803 \n0.2244 \n2.908 \n0.981 \n23.47 \n24 mA \n970 \n0.82 \n0.2222 \n2.6 \n0.616 \n23.39 \n \nOutput Characteristics, FA-HM10-M2245, FA-HM11-M4405, FA-HM10-M4485, FA-\nHM10-M8205 \nGeneral Purpose Outputs 1, 2, 3 and 4 Fast Switching  \nOn these models, all general-purpose outputs support a fast switching mode with ground of the user load \nconnected to pin 3 (General Input/Output Common Ground). Test conditions are with front plate temperature \n~60C. \n \nOutput Common \nPower \nOutput \nRload \ntd1 (us) \ntrise (µs) \ntd2 (µs) \ntfall (µs) \nVout (V) \nCurrent \nTest (ohm) Leading \nDelay \nRise Time \nTrailing \nDelay \nFall Time \n5V \n8 mA \n561 \n1 \n0.7 \n3.64 \n0.5 \n4.53 \n16 mA \n277 \n1 \n0.7 \n3.48 \n0.659 \n4.45 \n24 mA \n182 \n1 \n0.7 \n3.32 \n0.65 \n4.37 \n12V \n8 mA \n1444 \n0.934 \n0.2321 \n2.88 \n0.949 \n11.49 \n16 mA \n713 \n0.945 \n0.2563 \n2.86 \n0.42 \n11.41 \n24 mA \n467 \n0.952 \n0.2739 \n2.78 \n0.224 \n11.33 \n24V \n8 mA \n2930 \n0.81 \n0.2079 \n3.542 \n1.639 \n23.57 \n16 mA \n1464 \n0.803 \n0.2244 \n2.908 \n0.981 \n23.47 \n24 mA \n970 \n0.82 \n0.2222 \n2.6 \n0.616 \n23.39 \n \n \n \n100  •  Technical Specifications \nFalcon™ 4-CLHS Series \nExternal Outputs: Using External TTL/LVTTL Drivers \nUser IO\nGround\nUser IO\nPower\nSignal 1\nCamera IO \nInterface\nLVTTL/TTL \nBuffer\nR\n(Pull-Down)\nR\n(Pull-Down) Signal 2\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\n( User IO Power )\n( Output 1 )\n( Output 2 )\n \nExternal Outputs: Using External LED Indicators \n• \nTwo external LEDs can be connected in the Common Cathode configuration. User IO\nGround\nUser IO\nPower\nCamera IO \nInterface\nR\nR\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\n( User IO Power )\n( Output 1 )\n( Output 2 )\nSet resistor (R) value to not \nexceed output current of \nIF = 30mA.\n! IF\nIF\n \n• \nAlternatively, one external LED can be connected in the Common Anode configuration."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 199, "content": "Falcon™ 4-CLHS Series \nTechnical Specifications  •  101 \nUser IO\nGround\nUser IO\nPower\nCamera IO \nInterface\nR\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\n( Output 1 )\n( Output 2 )\n! IF\nSet resistor (R) value to not \nexceed output current of \nIF = 30mA.\nOnly one Output (1 or 2) can \nbe used in this configuration. Using Falcon4 Outputs to drive other Falcon4 Inputs \n• \nA synchronization method where one Falcon4 camera signals other Falcon4 cameras. • \nNote: One Falcon4 output can drive a maximum of three Falcon4 inputs, as illustrated below. 102  •  Technical Specifications \nFalcon™ 4-CLHS Series \nUser IO\nPower\nCamera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\n(GPO_CMD_PWR)\n(GPO_P0)\n(GPO_P1)\nDo not exceed more then three \nslave cameras per GPO line.\n! Camera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 1)\nCamera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 2)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 200, "content": "Camera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 3)\nCamera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 4) Camera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 5)\nCamera IO \nInterface\n10\n9\n8\n7\n6\n5\n4\n3\n2\n1\nCamera (Slave 6) User IO\nGround\n(GPI_CMD_GND)\n(GPI_P0)\n(GPI_P1)\nUser IO\nGround\n(GPI_CMD_GND)\n(GPI_P0)\n(GPI_P1)\nUser IO\nGround\n(GPI_P0)\n(GPI_P1)\nGPI P0 or GPI P1 can be used as \ninput trigger.\n! User IO\nGround\n(GPI_CMD_GND)\n(GPI_P0)\n(GPI_P1)\nUser IO\nGround\n(GPI_P0)\n(GPI_P1)\nUser IO\nGround\n(GPI_P0)\n(GPI_P1)\n(GPI_CMD_GND)\n(GPI_CMD_GND)\n(GPI_CMD_GND)\n \n \n \nFalcon™ 4-CLHS Series \nDeclarations of Conformity  •  103 \nDeclarations of Conformity \nCopies of the Declarations of Conformity documents are available on the product page on the Teledyne DALSA \nwebsite or by request. FCC Statement of Conformance \nThis equipment complies with Part 15 of the FCC rules. Operation is subject to the following conditions:  \n1."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 201, "content": "The product may not cause harmful interference; and \n  \n2. The product must accept any interference received, including interference that may cause undesired \noperation. FCC Class A Product \nThis equipment has been tested and found to comply with the limits for a Class A digital device, pursuant to part \n15 of the FCC Rules. These limits are designed to provide reasonable protection against harmful interference \nwhen the equipment is operated in a commercial environment. This equipment generates, uses, and can radiate \nradio frequency energy and, if not installed and used in accordance with the instruction manual, may cause \nharmful interference to radio communications. Operation of this equipment in a residential area is likely to cause \nharmful interference in which case the user will be required to correct the interference at his own expense."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 202, "content": "Changes or modifications not expressly approved by the party responsible for compliance could void the user's \nauthority to operate the equipment. This equipment is intended to be a component of a larger industrial system. CE Declaration of Conformity \nTeledyne DALSA declares that this product complies with applicable standards and regulations. Changes or modifications not expressly approved by the party responsible for compliance could void the user's \nauthority to operate the equipment. This product is intended to be a component of a larger system and must be installed as per instructions to ensure \ncompliance. 104  •  Additional Reference Information \nFalcon™ 4-CLHS Series \nAdditional Reference Information \nChoosing a Lens with the Correct Image Circle  \nFalcon4 requires a lens with an image circle specification to fully illuminate the sensor."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 203, "content": "The following section \ngraphically shows the minimum lens image circle for each model along with alternative lens types. Brief \ninformation on other lens parameters to consider follows those sections. Lens Options for M2240, M4400 and M4480 \nThe following figure shows the lens image circles relative to Falcon4-CLHS models using the Lince 2.8 M and \nLince 11M sensors. Cameras with a M42 screw mount need image circles exceeding the surface of the sensors. 30.8 mm minimum\nImage Circle\nE2V Lince 2.8M (M2240) and \nLince 11M (M4400, M4480) Falcon™ 4-CLHS Series \nAdditional Reference Information  •  105 \nLens Options for M6200 and M8200 \nThe following figure shows the lens image circles relative to Falcon4-CLHS models using the Emerald 37M and \nEmerald 67M sensors."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 204, "content": "Cameras with a M42 screw mount need image circles exceeding the surface of the sensors. 30.8 mm Image Circle\nE2V Emerald 67M\n(M8200 model)\n29.0 mm image circle\nE2V Emerald 37M\n(M6200 model)\n21.8 mm image circle\n \n \n \n \n106  •  Additional Reference Information \nFalcon™ 4-CLHS Series \nAdditional Lens Parameters (application specific) There are other lens parameters that are chosen to meet the needs of the vision application. These parameters \nare independent of the Falcon4. A vision system integrator or lens specialist should be consulted when choosing \nlenses since there is a tradeoff between the best lenses and cost. An abridged list of lens parameters follows – all \nof which need to be matched to the application."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 205, "content": "• \nFocal Length: Defines the focus point of light from infinity. See Camera Specifications — Back Focal \nDistance.  \n• \nField of View: A lens is designed to image objects at some limited distance range, at some positive or \nnegative magnification. This defines the field of view. • \nF-Number (aperture): The lens aperture defines the amount of light that can pass. Lenses may have fixed or \nvariable apertures. Additionally, the lens aperture affects Depth of Field which defines the distance range \nwhich is in focus when the lens is focus at some specific distance. • \nImage Resolution and Distortion: A general definition of image quality. A lens with poor resolution appears \nout of focus when used to image fine details. • \nAberrations (defect, chromatic, spherical): Aberrations are specific types of lens faults affecting resolution \nand distortion. Lens surface defects or glass faults distort all light or specific colors. Aberrations are typically \nmore visible when imaging fine details."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 206, "content": "• \nSpatial Distortions: Describes non-linear lens distortions across the field of view. Such distortion limits the \naccuracy of measurements made with that lens. Falcon™ 4-CLHS Series \nAdditional Reference Information  •  107 \nOptical Considerations \nThis section provides an overview to illumination, light sources, filters, lens modeling, and lens magnification. Each of these components contribute to the successful design of an imaging solution. Illumination The wavelengths and intensity of light required to capture useful images vary per application."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 207, "content": "The image will be \naffected by speed, spectral characteristics, exposure time, light source characteristics, environmental and \nacquisition system specifics, etc. Look at Teledyne DALSA’s Knowledge Center for articles on this potentially \ncomplicated issue. Exposure settings have more effect than illumination. The total amount of energy (which is related to the total \nnumber of photons reaching the sensor) is more important than the rate at which it arrives. Example:  5 J/cm2 can be achieved by exposing 5 mW/cm2 for 1 ms or exposing 5 W/cm2 for 1 s. Light Sources \nKeep these guidelines in mind when selecting and setting up a light source: \n• \nLED light sources are inexpensive and provide a uniform field with a longer life span compared to other light \nsources."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 208, "content": "• \nHalogen and fiber-optic light sources provide very little blue relative to IR. • \nSome light sources age and produce less illumination in some areas of the spectrum. Back Focal Variance when using any Filter \nInserting a filter between a lens and sensor changes the back focal point of the lens used. A variable focus lens \nsimply needs to be adjusted, but in the case of a fixed focus lens, the changed focal point needs correction. The following simplified illustration describes this but omits any discussion of the optics, physics, and math behind \nthe refraction of light through glass filter media. 108  •  Additional Reference Information \nFalcon™ 4-CLHS Series \nsensor surface\n(focal plane)\nFocal Point with \nfilter is behind \nsensor surface\nFilter\nIllustration: Change of Focal \nPoint with inserted filter\nIncident Light\n(from Lens)"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 209, "content": "In this example when a glass filter is inserted between the lens and the camera sensor, the focal point is now \nabout 1/3 of the filter thickness behind the sensor plane. Falcon™ 4-CLHS Series \nAdditional Reference Information  •  109 \nLens Modeling \nAny lens surrounded by air can be modeled for camera purposes using three primary points: the first and second \nprincipal points and the second focal point. The primary points for a lens should be available from the lens data \nsheet or from the lens manufacturer. Primed quantities denote characteristics of the image side of the lens. That \nis, h is the object height and h is the image height. The focal point is the point at which the image of an infinitely distant object is brought to focus."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 210, "content": "The effective focal \nlength (f) is the distance from the second principal point to the second focal point. The back focal length (BFL) is \nthe distance from the image side of the lens surface to the second focal point. The object distance (OD) is the \ndistance from the first principal point to the object. Primary Points in a Lens System \n \nFigure 1: Primary Points in a Lens System \nMagnification and Resolution The magnification of a lens is the ratio of the image size to the object size: \n𝑚= ℎ′/ℎ \nWhere m is the magnification, h’ is the image height (pixel size) and h is \nthe object height (desired object resolution size). By similar triangles, the magnification is alternatively given by: \n𝑚= 𝑓′/𝑂𝐷 \nWhere f’ is the focal length and OD is the target object distance."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 211, "content": "These equations can be combined to give their most useful form:  \nℎ′/ℎ= 𝑓′/𝑂𝐷 This is the governing equation for many object and image plane \nparameters. Example: An acquisition system has a 512 x 512-element 10 µm pixel pitch, a lens with an effective focal length \nof 45 mm. For each pixel in the image sensor to correspond to 100 µm in the object space, using the preceding \nequation, the object distance must be 450 mm (0.450 m). (10 𝜇𝑚)/(100 𝜇𝑚) = (45 𝑚𝑚)/𝑂𝐷 \n𝑂𝐷= 450 𝑚𝑚 (0.450 𝑚) \n \n \n110  •  Additional Reference Information \nFalcon™ 4-CLHS Series \nSensor Handling Instructions \nThis section reviews procedures for handling, cleaning or storing the camera."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 212, "content": "The sensor must be kept clean and \naway from static discharge to maintain design performance. Electrostatic Discharge and the Sensor \nCamera sensors containing integrated electronics are susceptible to damage from electrostatic discharge (ESD). Electrostatic charge introduced to the sensor window can induce charge buildup on the underside of the window. The dry nitrogen gas in the sensor package cavity cannot readily dissipate the ESD. Problems such as higher \nimage lag or non-uniform response may occur. NOTE"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 213, "content": "The charge normally dissipates within 24 hours and the sensor returns to normal operation. Important: Charge buildup will affect the camera’s Flat-Field Correction calibration. To avoid an erroneous \ncalibration, ensure that you perform Flat-Field Correction only after a charge buildup has dissipated over 24 \nhours. Protecting Against Dust, Oil and Scratches \nThe sensor window is part of the optical path and must be handled with extreme care. Dust can obscure pixels producing dark patches on the sensor image. Dust is most visible when the illumination is \ncollimated."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 214, "content": "The dark patches shift position as the angle of illumination changes. Dust is normally not visible when \nthe sensor is positioned at the exit port of an integrating sphere where illumination is diffused. Blowing compressed air on the window will remove dust particles unless they are held by an electrostatic charge. In this case, either an ionized air blower or a wet cleaning is necessary. Touching the surface of the window will leave oily residues. Using rubber finger cots and rubber gloves can \nprevent oil contamination."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 215, "content": "Avoid friction between the rubber and window or electrostatic charge build up may \ndamage the sensor. When handling or storing the camera without a lens always install the protective cap. NOTE \nWhen exposed to uniform illumination, a scratched window will normally display brighter pixels adjacent to \ndarker pixels. The location of these pixels will change with the angle of illumination. Falcon™ 4-CLHS Series \nAdditional Reference Information  •  111 \nCleaning the Sensor Window The following steps describe various cleaning techniques to clean minor dust particles and accidental fingerprints."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 216, "content": "NOTE \nAvoid using canned air as it contains particulates that can increase the contamination of the sensor window. • \nDALSA recommends the use of an ionized air gun and compressor to blow off the sensor. • \nUse compressed air to blow off loose particles. This step alone is usually sufficient to clean the sensor \nwindow. Avoid moving or shaking the compressed air container and use short bursts of air while moving the \ncamera in the air stream. Agitating the container will cause condensation to form in the air stream. NOTE \nExtended airbursts will chill the sensor window causing more condensation. Condensation when left to dry \nnaturally will deposit particles on the sensor. • \nUse lint-free ESD-safe cloth wipers. The Anticon Gold 9”x 9” wiper made by Milliken is both ESD safe and \nsuitable for class 100 environments. Another ESD acceptable wiper is the TX4025 from Texwipe. • \nAn alternative to ESD-safe cloth wipers is Transplex swabs that have desirable ESD properties."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 217, "content": "There are \nseveral varieties available from Texwipe. • \nWipe the window carefully and slowly when using these products. NOTE \nDo not use regular cotton swabs, since these can introduce static charge to the window surface. 112  •  Additional Reference Information \nFalcon™ 4-CLHS Series \nI/O Cable Accessories \nTeledyne DALSA provides optional I/O cable assemblies. Users wishing to build their I/O cabling by starting from \navailable cable packages should consider these popular assemblies described below. Contact Sales for pricing \nand delivery."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 218, "content": "Users also may order cable assembly quantities directly from Alysium-Tech or Hewtech. In such cases use the \nmanufacturer’s part number shown on the cable assembly engineering drawing. Cable Manufacturers Contact Information \n \nFor Information contact:  \n(see their web site for worldwide \noffices) \nAlysium-Tech \n101 Montgomery Street, Suite 2050 \nSan Francisco, CA 94104 \nPhone: ************ \nFax: ************ \nhttps://www.alysium.com/ \n \nHIRAKAWA HEWTECH CORP \nSales Division. 3-28-10 Minami-Ooi, Shinagawa, Tokyo 140-8551 \nTel: 03-5493-1711 \nhttps://www.hewtech.co.jp/e/index.html \n \n \nFalcon™ 4-CLHS Series \nAdditional Reference Information  •  113 \nCable Assembly G5-AIOC-BLUNT2M \n \n \n \n114  •  Additional Reference Information \nFalcon™ 4-CLHS Series \n \n \n \nFalcon™ 4-CLHS Series \nAdditional Reference Information  •  115 \nGeneric Power Supply with no I/O \n \n \n \n116  •  Troubleshooting \nFalcon™ 4-CLHS Series \nTroubleshooting \nOverview \nIn rare cases an installation may fail or there are problems in controlling and using the camera. This section \nhighlights issues or conditions which may cause installation problems. Emphasis is on the user to perform \ndiagnostics with the tools provided plus methods are described to correct the problem."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 219, "content": "Problem Type Summary \nProblems are either installation issues due to cabling or power, or setup errors with the frame grabber \nconfiguration. Before Contacting Technical Support \nCarefully review the issues described in this Troubleshooting section. To aid Teledyne DALSA personnel when \nsupport is required, the following should be included with the request for support. • \nFrom the Start menu, select Teledyne Dalsa Sapera LT > Sapera Log Viewer. • \nFrom its File menu click on Save Messages to generate a log text file. • \nReport the version of camera Firmware and Sapera version used."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 220, "content": "• \nReport the frame grabber brand and model used. Provide specifications for any third party frame grabber \nused. Device Available with Operational Issues \nThis section considers issues with frame grabbers, cabling, multiple cameras and camera exposure. Firmware Updates \nAs a general rule any installation must include the firmware update procedure to ensure having the latest build \n(see Updating Firmware via File Access in CamExpert). NOTE \nA Falcon4 that had a fault with a firmware update will automatically recover by booting with the previous \nfirmware version. NOTE \nNew cameras installed in previously deployed systems are fully backward compatible with the older vision \napplication."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 221, "content": "Falcon™ 4-CLHS Series \nTroubleshooting  •  117 \nPower Failure During a Firmware Update–Now What? Don’t panic! There is far greater chance that the host computer OS is damaged during a power failure than any \npermanent problems with the Falcon4. When power returns and the computer restarts, follow the procedure \nbelow to complete the firmware update. • \nConnect power to the camera. The processor knows that the firmware update failed. • \nThe camera will boot with the previous version of firmware and will operate normally. • \nPerform the firmware update procedure again. Cabling and Communication Issues \nPower supply problems: \n• \nVerify the DC power supply voltage and I/O cable wiring. Communication Problems: \n• \nUse quality CX4 AOC (Active Optical Cable) cables. For I/O, use quality shielded I/O cables. This can \neliminate issues in a high EMI environment."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 222, "content": "Purchase CX4 cables from certified sources. • \nUse the Sapera Log Viewer tool for error conditions:  \n• \nFrom the Start menu, select Teledyne Dalsa Sapera LT > Sapera Log Viewer. • \nStart an acquisition program, such as CamExpert. • \nReview the log output for error messages. Camera is Functional, Frame Rate is as Expected, but Image is \nBlack \n• \nUsing CamExpert, set the Falcon4 to output its Internal Pattern Generator (with external trigger Off). This step \nis typically done for any camera installation to quickly verify the camera and its software package."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 223, "content": "• If using an external trigger exposure (via the frame grabber), verify the trigger source rate and pulse width \ncoming from the grabber parameters. • \nVerify that the lens iris is open. • \nAim the camera at a bright light source. • \nCheck that the programmed exposure duration is not too short or set it to maximum. 118  •  Revision History \nFalcon™ 4-CLHS Series \nRevision History \n \nRevision \nDate \nMajor Change Description \n03-032-20295-00 \nAugust 18, 2021 \nInitial release. 03-032-20295-01 \nFebruary 2, 2022 \nBinning and Lens Shading Correction. 03-032-20295-02 \nJuly 18, 2022 \nNew Long Exposure Mode feature. Update of Output Dynamic Range and Full Well \ncharge values. Update of Responsivity graph. 03-032-20295-03 \nSeptember 29, 2022 \nAdditional model. New features: Cycling Preset, Metadata, Multi ROI, LUT, Digital Gain."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 224, "content": "03-032-20295-04 \nDecember 5, 2022 \nNew M8200 model. Updates in metadata (factory gain). 03-032-20295-05 \nApril 25, 2023 \nNew M2240 model. 03-032-20295-06 \nAugust 29, 2023 \nNew feature: cycling of lens shading coefficients. 03-032-20295-07 \nDecember 18, 2023 New M6200 model. Corrected readout time for M8200 for 10-bit in specifications table. Updated responsivity curve for M8200. Updated accessories list. Added firmware .cbf \nfile name for M8200. A few features the M8200 are expected in a future release but \nwere incorrectly included as functional–see footnote in Common Specifications and \nM8200 specifications table. Corrected Exposure Time Range and Analog Gain for \nM8200 in table."}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 225, "content": "Precision: user defective pixel replacement map available in all models \nexcept M2240. New Defective Replacement Algorithm for M8200. Added Warning about \ngrounding when installing frame grabber. Added Flat Field category (subcategory of \nData Processing) for M6200 and M8200. Minor updates to feature descriptions. Falcon™ 4-CLHS Series \nContact Information  •  119 \nContact Information  \nSales Information \n \nVisit our web site: \nhttps://www.teledynedalsa.com/en/products/imaging/  \nEmail: \nmailto:<EMAIL>  \n \nCanadian Sales \nCanadian Sales \nTeledyne DALSA — Head office  \n605"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 226, "content": "McMurray Road \nWaterloo, Ontario \nCanada  N2V 2E9 Tel: ******-886-6000 \nFax:  ******-886-8023 \n \nTeledyne DALSA — Montreal office \n880 Rue McCaffrey \nSaint-Laurent, Quebec \nCanada  H4T 2C7 \nTel: ******-333-1301 \nFax:  ******-333-1388 \n \nUSA Sales \nEuropean Sales \nTeledyne DALSA — Billerica office \n700 Technology Park Drive \nBillerica, MA  \nUSA  01821 Tel: ******-670-2000 \nFax: ******-670-2010 \n \nTeledyne DALSA GMBH \nLise-Meitner-Str. 7 \n82152 Krailling (Munich),  \nGermany \nTel:  +49 – 89 89545730  \n<EMAIL>"}, {"source_file": "03-032-20295-07_Falcon4-CLHS_Series_User (1).pdf", "chunk_number": 227, "content": "Asia Pacific Sales \nAsia Pacific Sales \nTeledyne DALSA Asia Pacific \nIkebukuro East 6F \n3-4-3 Higashi <PERSON>bukuro, Toshima-ku \nTokyo, 170-0013  \nJapan \nTel: +81 3-5960 6353 \nFax: +81 3-5960 6354 \n<EMAIL> Teledyne DALSA Asia Pacific  \nRoom 904, Block C, Poly West Bund Center \n75 Rui Ping Road Shanghai 200032 China \nTel:  +86-21-60131571 \n \n<EMAIL> \n \nTechnical Support \n \nSubmit any support question or request via our web site: \n \nTechnical support form via our web page: \nSupport requests for imaging product installations,  \nSupport requests for imaging applications \nhttps://www.teledynedalsa.com/en/support/options/  \n \nCamera support information \n \nProduct literature and driver updates"}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 1, "content": ""}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 2, "content": "Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 1 \n \n \nTeledyne DALSA • 880 Rue McCaffrey • St-Laurent, Québec, H4T 2C7 • Canada \nhttps://www.teledynedalsa.com/en/products/imaging/cameras/falcon4-clhs/  \n \nFA-ANHS01-v3: Falcon4-CLHS Application Note \nConfiguring Falcon4-C<PERSON><PERSON> and Teledyne \nDALSA Frame Grabbers \nFor Falcon4-CLHS models with P/N: FA-Hxxx-xxxxx \nOverview \nFalcon4-CLHS cameras require a connection to a frame grabber to acquire images. This application note describes how to configure a Falcon4-CLHS with a Teledyne \nDALSA CLHS Xtium2 series frame grabber. The Falcon4-CLHS supports the CLHS device discovery methodology providing plug-\nand-play capability. GenICam implementation allows compatibility with Teledyne \nDALSA or third-party CLHS frame grabbers that support the CLHS X-Protocol \n(CLHS M-Protocol not supported). Teledyne DALSA Xtium2 series frame grabbers \nsupport CLHS X-Protocol. The Falcon4-CLHS series includes the following models: \n• \nFalcon4-CLHS M4480 (FA-HM00-M4485): 7 data lane output."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 3, "content": "• \nFalcon4-<PERSON><PERSON><PERSON> M8200 (FA-HM10-M8205): 7 data lane output. • \nFalcon4-CLHS M6200 (FA-HM10-M6205): 7 data lane output. Models supported by Teledyne DALSA Xtium2-CL<PERSON> PX8 (OR-A8S0-PX870) \nframe grabber. \n \n• \nFalcon4-<PERSON><PERSON><PERSON> M4400 (FA-HM01-M4405): 4 data lane output. Model supported by Teledyne DALSA Xtium2-CLHS PX8 (OR-A8S0-PX870) or \nXtium2-CLHS PX8 LC (OR-A8S0-PX840) frame grabber. The maximum sustained bandwidth from a Falcon4-CLHS M4480 \ncamera to an Xtium2-CLHS PX8 is up to ~6.7 GB/s (approximately \n505 fps at full resolution / 8-bit). To reach the full camera bandwidth \n(600 fps), two frame grabbers, using data forwarding, are required."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 4, "content": "For \nthe other Falcon4 models, a single frame grabber can handle the \nmaximum bandwidth of a camera. Refer to the frame grabber \ndocumentation for more information. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 2 \nRequirements & Installation \nPrerequisites The following table lists the recommended Falcon4-CLHS firmware and software for \nthe camera models. FALCON4-\nCLHS Model \nFalcon4-CLHS Firmware Design \nSoftware \nSDK \nM4480 \nM4400 \nFalcon4-CLHS_e2v_11M_STD_Firmware_256.101.cbf  \nor higher \nSapera LT 8.60 \n(or higher) \nM6200 \nM8200 \nFalcon4-CLHS_e2v_37-67M_STD_Firmware_xx.xx.cbf  \nor higher \nSapera LT 8.70 \n(or higher) \nSoftware \nSapera LT SDK (full version), the image acquisition and control software \ndevelopment kit (SDK) for Teledyne DALSA cameras is available for download from \nthe Teledyne DALSA website: \nhttp://teledynedalsa.com/imaging/support/downloads/sdks/ \nIf the required version is not available, contact your Teledyne DALSA representative. Sapera LT includes the CamExpert application, which provides a graphical user \ninterface to access camera features for configuration and setup."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 5, "content": "Hardware \nA frame grabber board such as the Teledyne DALSA Xtium2-CLHS PX8 / PX8 LC is the \nrecommended computer interface. Falcon4 Model \nTeledyne DALSA Frame Grabber \nPart Number \nM4400 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \nXtium2 CLHS PX8 LC \nOR-A8S0-PX840 \nM4480 \nM6200 \nM8200 \nXtium2 CLHS PX8 \nOR-A8S0-PX870 \n \nFollow the installation instructions from the board’s User Manual for the computer \nrequirements, installation, and update of the board driver. The latest board drivers are available from the Teledyne DALSA website: \nhttps://www.teledynedalsa.com/en/support/downloads-center/device-drivers/  \nDoc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 3 \nCamera Link HS Cables Overview and Resources \nThe camera uses a Camera Link HS SFF-8470 (CX4) cable; AOC (Active Optical \nConnectors) cables are recommended due to the high-bandwidth CLHS X-Protocol \n(C3 copper cables < 2m may work but are not recommended). Note: CX4 AOC cables are directional; ensure that the connector \nlabelled “Camera” and “FG” are attached accordingly to the camera and \nframe grabber. Visit our web site for additional information on the CLHS interface: \nhttps://www.teledynedalsa.com/en/learn/knowledge-center/clhs/  \nFor additional information on cables and their specifications, visit the following web \nsites and search for “Camera Link HS” cables: \n \nComponents Express \nhttp://www.componentsexpress.com/ \nFiberStore \nhttps://www.fs.com  \n \nCamera Power  \nCameras with part number FA-HMxx-xxxxx support Power via the Auxiliary Connector \n(12 to 24 Volt DC). Refer to the Falcon4-CLHS User Manual for cable accessories or \nmating connector details."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 6, "content": "The frame grabber PoCL (Power-over-Cable) powers the electronics in \nthe Active Optical Cable (AOC) module. This frame grabber feature \nshould not be disabled for normal operation. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 4 \nInstallation Procedure \nThe following steps summarize the installation procedure: \n• \nInstall the Xtium2-CLHS PX8 (or Xtium2-CLHS PX8 LC) into an available PCI \nExpress x8 Gen3 slot. • \nTurn on the computer. • \nDownload and install the Sapera LT SDK or its runtime library: \n• \nversion 8.60 or newer required for Models M4480, M4400 \n• \nversion 8.70 or newer required for Models M6200, M8200 \n• \nDownload and install the Xtium2-CLHS PX8 Sapera board driver: \n• \nversion 1.31 or newer required for Models M4480, M4400 \n• \nversion 1.40 or newer required for Models M6200, M8200 \n• \nReboot the computer. • \nConnect the Falcon4-CLHS with a CLHS camera cable to the CLHS frame grabber; \npower the camera using an appropriate power supply."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 7, "content": "• \nThe Falcon4-CLHS status LED will indicate power and the Device / Host connection \nwith a steady green color when connected. Refer to the section “Camera Status \nLED Indicator” in the camera manual for a complete list of Status LED indicators. Start Sapera CamExpert \nThe Sapera CamExpert application is included as part of the Sapera LT SDK. It is \nTeledyne DALSA’s camera and frame grabber interfacing tool that allows you to \nquickly validate hardware setup, change parameter settings, and test image \nacquisition. It is available from the Windows Start menu or desktop shortcut. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 5 \nSelect the Frame Grabber & Camera"}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 8, "content": "If there is only one Teledyne DALSA frame grabber, the Device list automatically has \nthe Xtium2-CLHS PX8 selected and the connected Falcon4-CLHS is also automatically \ndetected as shown in the image below. CamExpert indicates the status of the data connections and signal integrity between \nthe camera and frame grabber. The CamExpert Video Status bar, below the Output \nMessages window, displays the connection status flags in green (OK) or red (error). The following screen capture shows that the Data Lane signals are correct and Frame \nValid and Line Valid signals are active. If the Camera is Not Automatically Detected \nVerify that the camera is properly powered and that the fiber optic cable is connected \ncorrectly to the appropriate connectors on the frame grabber and camera; cables are \nuni-directional and connectors are labelled “Camera” and “F G” (frame grabber). Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 6 \nUpload New Camera Firmware \nWith the Falcon4-CLHS detected the user should upload new firmware if available."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 9, "content": "Using CamExpert, verify the current camera firmware by selecting the Camera \nInformation category and checking the Firmware Version feature. New firmware versions are available in the file download area of the Teledyne DALSA \nweb site. Download the latest release to the computer used with the Falcon4. To Upload New Firmware \n• \nSelect the File Access Control category and click Setting. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 7 \n• \nIn the File Access Control dialog, from the Type list select Device Firmware and \nclick <PERSON>rows<PERSON> to select the required firmware file. • \nClick Upload (to Camera) and restart the camera when prompted."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 10, "content": "Important: File upload rates are fixed (as per the CLHS standard) at \n20 Mb/s. As an example, a firmware file upload process will take about \n2½ minutes. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 8 \nVerify Basic Acquisition \nTo verify basic acquisition, the camera can output a test pattern to validate that \nparameter settings are correctly configured between the camera and frame grabber. • \nIn the Image Format category, select Test Pattern – Grey Diagonal Ramp Moving. • \n(For models M6200 & M8200 only) In the Basic Timing board category, click the \nCamera Sensor Geometry Setting value, and select 1X-2YE Two Channel Converge \nas depicted."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 11, "content": "Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 9 \n• \nOn the Display toolbar, click Fit to Screen to view the complete acquisition in the \ndisplay window (the actual acquisition data is unmodified). • Click Grab to view the diagonal ramp acquisition. Key features to verify include: \n• \nData Lanes \n• \nImage Format (pixel depth, pixel format, image height and width) For example, for frame grabbers, the Basic Timing category includes the Data Lanes \nand Pixel Depth parameters: \n \nDoc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 10 \nThe Image Buffer and ROI categories include the Image Width, Image Height, and \nImage Buffer format parameters. For the Falcon4-CLHS, the Image Format category provides the required feature \nsettings."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 12, "content": "The Transport Layer category includes the Next CLHS Device Configuration feature, \nwhich describes the camera cable and data lanes. Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 11 \n \nWhen the imaging setup is validated and working correctly: \n• \nUse CamExpert to explore the Falcon4 camera feature set and the Xtium2-CLHS \nPX8 / PX8 LC parameter set. • Use the individual product’s User Manuals to explore the capabilities of this \nimaging system pair. • Develop your custom imaging application with the Sapera LT API."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 13, "content": "Doc #: FA-ANHS01-V3 \nSeptember 29, 2022 \nPage 12 \nFast Readout Mode The Fast Readout Mode feature (available in the Camera Control category) determines \nthe sensor readout rate. When enabled, the sensor readout is faster, allowing for a \nhigher maximum frame rate. Note that the Fast ReadOut Mode feature is only available in the Falcon4-CLHS M4400 \nand M4480 models. The recommended Fast Readout Mode enable state depends on the frame grabber \nconfiguration and acquisition scenario: Frame Grabber Configuration \nFast Readout Mode \nSingle Xtium2-CLHS PX8 frame grabber \nAcquiring and SUSTAINING HIGH frame rate capture   \n(for example, 505 fps in full resolution)."}, {"source_file": "Getting Started with Falcon4-CLHS and Teledyne Frame Grabbers FA-ANHS01 (1).pdf", "chunk_number": 14, "content": "Off \nSingle Xtium2-CLHS PX8 frame grabber Acquiring in short bursts at very high-speed frame rate capture  \n(for example, triggered camera acquisition at maximum frame \nrate to capture a sequence of a few frames (less than the \nnumber of frame grabber image buffers)). Active \n2 Xtium2-CLHS PX8 frame grabbers with data forwarding \nAcquiring and SUSTAINING frame rate capture   \n(for example, greater than 505 fps in full resolution)."}]