{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AIAgentChatbot() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: \"bot\",\n    content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n    timestamp: new Date()\n  }]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n    testBackendConnection();\n  }, []);\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim()) return;\n\n    // Add user message to chat\n    const userMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content: query,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setLoading(true);\n    setError(\"\");\n    const currentQuery = query;\n    setQuery(\"\"); // Clear input immediately\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query: currentQuery\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        // Add bot response to chat\n        const botMessage = {\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: data.answer,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, botMessage]);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n    setLoading(false);\n  }\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const renderMessages = () => {\n    return messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.type}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-text\",\n          children: message.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message-time\",\n          children: formatTime(message.timestamp)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, message.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83E\\uDD16 AI Agent Chatbot\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"subtitle\",\n      children: \"Ask me anything about the technical documentation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"chat-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"queryInput\",\n        value: query,\n        onChange: e => setQuery(e.target.value),\n        placeholder: \"Ask me anything (e.g., 'How do I configure the camera?', 'What are the installation steps?')\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        id: \"submitButton\",\n        disabled: loading,\n        children: loading ? \"🧠 Thinking...\" : \"💬 Ask AI\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"\\uD83E\\uDDE0 AI is thinking...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results\",\n      children: displayChatAnswer()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n}\n_s(AIAgentChatbot, \"XI0Gl11PzOvUhnDyfnhnkCRfenw=\");\n_c = AIAgentChatbot;\nvar _c;\n$RefreshReg$(_c, \"AIAgentChatbot\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AIAgentChatbot", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "loading", "setLoading", "error", "setError", "testBackendConnection", "response", "fetch", "data", "json", "console", "log", "handleSubmit", "e", "preventDefault", "trim", "userMessage", "now", "prev", "<PERSON><PERSON><PERSON><PERSON>", "method", "headers", "body", "JSON", "stringify", "ok", "botMessage", "answer", "message", "formatTime", "toLocaleTimeString", "hour", "minute", "renderMessages", "map", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "value", "onChange", "target", "placeholder", "required", "disabled", "displayChatAnswer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport './App.css';\n\nexport default function AIAgentChatbot() {\n  const [query, setQuery] = useState(\"\");\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: \"bot\",\n      content: \"👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!\",\n      timestamp: new Date()\n    }\n  ]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n\n    testBackendConnection();\n  }, []);\n\n  async function handleSubmit(e) {\n    e.preventDefault();\n    if (!query.trim()) return;\n\n    // Add user message to chat\n    const userMessage = {\n      id: Date.now(),\n      type: \"user\",\n      content: query,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setLoading(true);\n    setError(\"\");\n\n    const currentQuery = query;\n    setQuery(\"\"); // Clear input immediately\n\n    try {\n      const response = await fetch(\"http://localhost:5000/api/chat/\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ query: currentQuery }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        // Add bot response to chat\n        const botMessage = {\n          id: Date.now() + 1,\n          type: \"bot\",\n          content: data.answer,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, botMessage]);\n      } else {\n        setError(data.error || \"Error processing request\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n\n    setLoading(false);\n  }\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  const renderMessages = () => {\n    return messages.map((message) => (\n      <div key={message.id} className={`message ${message.type}`}>\n        <div className=\"message-content\">\n          <div className=\"message-text\">\n            {message.content}\n          </div>\n          <div className=\"message-time\">\n            {formatTime(message.timestamp)}\n          </div>\n        </div>\n      </div>\n    ));\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>🤖 AI Agent Chatbot</h1>\n      <p className=\"subtitle\">Ask me anything about the technical documentation</p>\n\n      <form className=\"chat-form\" onSubmit={handleSubmit}>\n        <input\n          type=\"text\"\n          id=\"queryInput\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          placeholder=\"Ask me anything (e.g., 'How do I configure the camera?', 'What are the installation steps?')\"\n          required\n        />\n        <button\n          type=\"submit\"\n          id=\"submitButton\"\n          disabled={loading}\n        >\n          {loading ? \"🧠 Thinking...\" : \"💬 Ask AI\"}\n        </button>\n      </form>\n\n      {error && (\n        <div className=\"error\">\n          {error}\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"loading\">\n          🧠 AI is thinking...\n        </div>\n      )}\n\n      <div className=\"results\">\n        {displayChatAnswer()}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,CACvC;IACEU,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,kFAAkF;IAC3FC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,QAAQ,CAAC,yEAAyE,CAAC;MACrF;IACF,CAAC;IAEDC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeO,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACtB,KAAK,CAACuB,IAAI,CAAC,CAAC,EAAE;;IAEnB;IACA,MAAMC,WAAW,GAAG;MAClBpB,EAAE,EAAEI,IAAI,CAACiB,GAAG,CAAC,CAAC;MACdpB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEN,KAAK;MACdO,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3Cd,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMe,YAAY,GAAG3B,KAAK;IAC1BC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,EAAE;QAC9Da,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEhC,KAAK,EAAE2B;QAAa,CAAC;MAC9C,CAAC,CAAC;MAEF,MAAMX,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACmB,EAAE,EAAE;QACf;QACA,MAAMC,UAAU,GAAG;UACjB9B,EAAE,EAAEI,IAAI,CAACiB,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBpB,IAAI,EAAE,KAAK;UACXC,OAAO,EAAEU,IAAI,CAACmB,MAAM;UACpB5B,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDL,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEQ,UAAU,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLtB,QAAQ,CAACI,IAAI,CAACL,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,QAAQ,CAAC,iBAAiB,GAAGD,KAAK,CAACyB,OAAO,CAAC;IAC7C;IAEA1B,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAM2B,UAAU,GAAI9B,SAAS,IAAK;IAChC,OAAOA,SAAS,CAAC+B,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOvC,QAAQ,CAACwC,GAAG,CAAEN,OAAO,iBAC1BvC,OAAA;MAAsB8C,SAAS,EAAE,WAAWP,OAAO,CAAC/B,IAAI,EAAG;MAAAuC,QAAA,eACzD/C,OAAA;QAAK8C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/C,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BR,OAAO,CAAC9B;QAAO;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1BP,UAAU,CAACD,OAAO,CAAC7B,SAAS;QAAC;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,GAREZ,OAAO,CAAChC,EAAE;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OASf,CACN,CAAC;EACJ,CAAC;EAED,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/C,OAAA;MAAA+C,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5BnD,OAAA;MAAG8C,SAAS,EAAC,UAAU;MAAAC,QAAA,EAAC;IAAiD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAE7EnD,OAAA;MAAM8C,SAAS,EAAC,WAAW;MAACM,QAAQ,EAAE7B,YAAa;MAAAwB,QAAA,gBACjD/C,OAAA;QACEQ,IAAI,EAAC,MAAM;QACXD,EAAE,EAAC,YAAY;QACf8C,KAAK,EAAElD,KAAM;QACbmD,QAAQ,EAAG9B,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;QAC1CG,WAAW,EAAC,8FAA8F;QAC1GC,QAAQ;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnD,OAAA;QACEQ,IAAI,EAAC,QAAQ;QACbD,EAAE,EAAC,cAAc;QACjBmD,QAAQ,EAAE9C,OAAQ;QAAAmC,QAAA,EAEjBnC,OAAO,GAAG,gBAAgB,GAAG;MAAW;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAENrC,KAAK,iBACJd,OAAA;MAAK8C,SAAS,EAAC,OAAO;MAAAC,QAAA,EACnBjC;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAvC,OAAO,iBACNZ,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAEzB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDnD,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBY,iBAAiB,CAAC;IAAC;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjD,EAAA,CAxIuBD,cAAc;AAAA2D,EAAA,GAAd3D,cAAc;AAAA,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}