{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\AI-Agent-Chatbot-main\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SearchPage() {\n  _s();\n  const [query, setQuery] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n    testBackendConnection();\n  }, []);\n  async function handleSearch(e) {\n    e.preventDefault();\n    setLoading(true);\n    setResults([]);\n    setError(\"\");\n    try {\n      const response = await fetch('http://localhost:5000/api/search/', {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          query\n        })\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setResults(data.matches);\n      } else {\n        setError(data.error || \"Error searching\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n    setLoading(false);\n  }\n  const displayResults = () => {\n    if (results.length === 0) {\n      return !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-results\",\n        children: \"No results found for your query.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 26\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: [\"Found \", results.length, \" results for \\\"\", query, \"\\\":\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), results.map((match, index) => {\n        const similarity = (match.similarity * 100).toFixed(1);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"result-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-header\",\n            children: [\"\\uD83D\\uDCC4 \", match.source_file, \" (Chunk \", match.chunk_number, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"result-content\",\n            children: match.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"similarity-score\",\n            children: [\"Similarity: \", similarity, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\uD83E\\uDD16 AI Agent Chatbot - Vector Search\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"search-form\",\n      onSubmit: handleSearch,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        id: \"queryInput\",\n        value: query,\n        onChange: e => setQuery(e.target.value),\n        placeholder: \"Enter your query here (e.g., 'camera configuration', 'frame rate', 'installation')\",\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        id: \"searchButton\",\n        disabled: loading,\n        children: loading ? \"Searching...\" : \"Search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"\\uD83D\\uDD0D Searching...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"results\",\n      children: displayResults()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}\n_s(SearchPage, \"RR8I5wgAz/Rvr8Ei6Rt+qQwEMF8=\");\n_c = SearchPage;\nvar _c;\n$RefreshReg$(_c, \"SearchPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "SearchPage", "_s", "query", "<PERSON><PERSON><PERSON><PERSON>", "results", "setResults", "loading", "setLoading", "error", "setError", "testBackendConnection", "response", "fetch", "data", "json", "console", "log", "handleSearch", "e", "preventDefault", "method", "headers", "body", "JSON", "stringify", "ok", "matches", "message", "displayResults", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "match", "index", "similarity", "toFixed", "source_file", "chunk_number", "content", "onSubmit", "type", "id", "value", "onChange", "target", "placeholder", "required", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/AI-Agent-<PERSON><PERSON><PERSON>-main/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport './App.css';\n\nexport default function SearchPage() {\n  const [query, setQuery] = useState(\"\");\n  const [results, setResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Test backend connection on component mount\n  useEffect(() => {\n    const testBackendConnection = async () => {\n      try {\n        const response = await fetch('http://localhost:5000/api/health');\n        const data = await response.json();\n        console.log('Backend status:', data);\n      } catch (error) {\n        setError('Cannot connect to backend server. Make sure it\\'s running on port 5000.');\n      }\n    };\n\n    testBackendConnection();\n  }, []);\n\n  async function handleSearch(e) {\n    e.preventDefault();\n    setLoading(true);\n    setResults([]);\n    setError(\"\");\n\n    try {\n      const response = await fetch('http://localhost:5000/api/search/', {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({ query }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setResults(data.matches);\n      } else {\n        setError(data.error || \"Error searching\");\n      }\n    } catch (error) {\n      setError(\"Network error: \" + error.message);\n    }\n\n    setLoading(false);\n  }\n\n  const displayResults = () => {\n    if (results.length === 0) {\n      return !loading && <div className=\"no-results\">No results found for your query.</div>;\n    }\n\n    return (\n      <div>\n        <h3>Found {results.length} results for \"{query}\":</h3>\n        {results.map((match, index) => {\n          const similarity = (match.similarity * 100).toFixed(1);\n          return (\n            <div key={index} className=\"result-item\">\n              <div className=\"result-header\">\n                📄 {match.source_file} (Chunk {match.chunk_number})\n              </div>\n              <div className=\"result-content\">\n                {match.content}\n              </div>\n              <div className=\"similarity-score\">\n                Similarity: {similarity}%\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"container\">\n      <h1>🤖 AI Agent Chatbot - Vector Search</h1>\n\n      <form className=\"search-form\" onSubmit={handleSearch}>\n        <input\n          type=\"text\"\n          id=\"queryInput\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          placeholder=\"Enter your query here (e.g., 'camera configuration', 'frame rate', 'installation')\"\n          required\n        />\n        <button\n          type=\"submit\"\n          id=\"searchButton\"\n          disabled={loading}\n        >\n          {loading ? \"Searching...\" : \"Search\"}\n        </button>\n      </form>\n\n      {error && (\n        <div className=\"error\">\n          {error}\n        </div>\n      )}\n\n      {loading && (\n        <div className=\"loading\">\n          🔍 Searching...\n        </div>\n      )}\n\n      <div className=\"results\">\n        {displayResults()}\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,eAAe,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACnC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;QAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,QAAQ,CAAC,yEAAyE,CAAC;MACrF;IACF,CAAC;IAEDC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,eAAeO,YAAYA,CAACC,CAAC,EAAE;IAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBF,UAAU,CAAC,EAAE,CAAC;IACdI,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;QAChEQ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEtB;QAAM,CAAC;MAChC,CAAC,CAAC;MAEF,MAAMW,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAIH,QAAQ,CAACc,EAAE,EAAE;QACfpB,UAAU,CAACQ,IAAI,CAACa,OAAO,CAAC;MAC1B,CAAC,MAAM;QACLjB,QAAQ,CAACI,IAAI,CAACL,KAAK,IAAI,iBAAiB,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,QAAQ,CAAC,iBAAiB,GAAGD,KAAK,CAACmB,OAAO,CAAC;IAC7C;IAEApB,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAMqB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxB,OAAO,CAACyB,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO,CAACvB,OAAO,iBAAIP,OAAA;QAAK+B,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACvF;IAEA,oBACEpC,OAAA;MAAAgC,QAAA,gBACEhC,OAAA;QAAAgC,QAAA,GAAI,QAAM,EAAC3B,OAAO,CAACyB,MAAM,EAAC,iBAAc,EAAC3B,KAAK,EAAC,KAAE;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACrD/B,OAAO,CAACgC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC7B,MAAMC,UAAU,GAAG,CAACF,KAAK,CAACE,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;QACtD,oBACEzC,OAAA;UAAiB+B,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtChC,OAAA;YAAK+B,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,eAC1B,EAACM,KAAK,CAACI,WAAW,EAAC,UAAQ,EAACJ,KAAK,CAACK,YAAY,EAAC,GACpD;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BM,KAAK,CAACM;UAAO;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNpC,OAAA;YAAK+B,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,cACpB,EAACQ,UAAU,EAAC,GAC1B;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GATEG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUV,CAAC;MAEV,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACEpC,OAAA;IAAK+B,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhC,OAAA;MAAAgC,QAAA,EAAI;IAAmC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5CpC,OAAA;MAAM+B,SAAS,EAAC,aAAa;MAACc,QAAQ,EAAE3B,YAAa;MAAAc,QAAA,gBACnDhC,OAAA;QACE8C,IAAI,EAAC,MAAM;QACXC,EAAE,EAAC,YAAY;QACfC,KAAK,EAAE7C,KAAM;QACb8C,QAAQ,EAAG9B,CAAC,IAAKf,QAAQ,CAACe,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;QAC1CG,WAAW,EAAC,oFAAoF;QAChGC,QAAQ;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFpC,OAAA;QACE8C,IAAI,EAAC,QAAQ;QACbC,EAAE,EAAC,cAAc;QACjBM,QAAQ,EAAE9C,OAAQ;QAAAyB,QAAA,EAEjBzB,OAAO,GAAG,cAAc,GAAG;MAAQ;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEN3B,KAAK,iBACJT,OAAA;MAAK+B,SAAS,EAAC,OAAO;MAAAC,QAAA,EACnBvB;IAAK;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA7B,OAAO,iBACNP,OAAA;MAAK+B,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAEzB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eAEDpC,OAAA;MAAK+B,SAAS,EAAC,SAAS;MAAAC,QAAA,EACrBH,cAAc,CAAC;IAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClC,EAAA,CArHuBD,UAAU;AAAAqD,EAAA,GAAVrD,UAAU;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}