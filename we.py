import weaviate

# Connect to Weaviate using v4 client
client = weaviate.connect_to_local(
    host="localhost",
    port=8080
)

try:
    # Get all collections (classes in v4 are called collections)
    collections = client.collections.list_all()
    print("Collections in Weaviate:")
    for collection_name in collections:
        print(f"- {collection_name}")

    # If you want to see the schema details
    if collections:
        print("\nDetailed schema information:")
        for collection_name in collections:
            collection = client.collections.get(collection_name)
            config = collection.config.get()
            print(f"\nCollection: {collection_name}")
            print(f"Properties: {[prop.name for prop in config.properties]}")
    else:
        print("No collections found in Weaviate.")

finally:
    # Always close the connection
    client.close()