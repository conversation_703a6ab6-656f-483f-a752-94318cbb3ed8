import React, { useState, useEffect } from "react";
import './App.css';

export default function AIAgentChatbot() {
  const [query, setQuery] = useState("");
  const [chatAnswer, setChatAnswer] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Test backend connection on component mount
  useEffect(() => {
    const testBackendConnection = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/health');
        const data = await response.json();
        console.log('Backend status:', data);
      } catch (error) {
        setError('Cannot connect to backend server. Make sure it\'s running on port 5000.');
      }
    };

    testBackendConnection();
  }, []);

  async function handleSubmit(e) {
    e.preventDefault();
    setLoading(true);
    setChatAnswer("");
    setError("");

    try {
      const response = await fetch("http://localhost:5000/api/chat/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json();

      if (response.ok) {
        setChatAnswer(data.answer);
      } else {
        setError(data.error || "Error processing request");
      }
    } catch (error) {
      setError("Network error: " + error.message);
    }

    setLoading(false);
  }

  const displayChatAnswer = () => {
    if (!chatAnswer && !loading) {
      return <div className="welcome-message">👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!</div>;
    }

    if (chatAnswer) {
      return (
        <div className="chat-response">
          <div className="answer-section">
            <h3>🤖 AI Assistant:</h3>
            <div className="answer-content">
              {chatAnswer}
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="container">
      <h1>🤖 AI Agent Chatbot</h1>
      <p className="subtitle">Ask me anything about the technical documentation</p>

      <form className="chat-form" onSubmit={handleSubmit}>
        <input
          type="text"
          id="queryInput"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Ask me anything (e.g., 'How do I configure the camera?', 'What are the installation steps?')"
          required
        />
        <button
          type="submit"
          id="submitButton"
          disabled={loading}
        >
          {loading ? "🧠 Thinking..." : "💬 Ask AI"}
        </button>
      </form>

      {error && (
        <div className="error">
          {error}
        </div>
      )}

      {loading && (
        <div className="loading">
          🧠 AI is thinking...
        </div>
      )}

      <div className="results">
        {displayChatAnswer()}
      </div>
    </div>
  );
}
