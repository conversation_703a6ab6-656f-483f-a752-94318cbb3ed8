import React, { useState, useEffect } from "react";
import './App.css';

export default function SearchPage() {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Test backend connection on component mount
  useEffect(() => {
    const testBackendConnection = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/health');
        const data = await response.json();
        console.log('Backend status:', data);
      } catch (error) {
        setError('Cannot connect to backend server. Make sure it\'s running on port 5000.');
      }
    };

    testBackendConnection();
  }, []);

  async function handleSearch(e) {
    e.preventDefault();
    setLoading(true);
    setResults([]);
    setError("");

    try {
      const response = await fetch('http://localhost:5000/api/search/', {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.matches);
      } else {
        setError(data.error || "Error searching");
      }
    } catch (error) {
      setError("Network error: " + error.message);
    }

    setLoading(false);
  }

  const displayResults = () => {
    if (results.length === 0) {
      return !loading && <div className="no-results">No results found for your query.</div>;
    }

    return (
      <div>
        <h3>Found {results.length} results for "{query}":</h3>
        {results.map((match, index) => {
          const similarity = (match.similarity * 100).toFixed(1);
          return (
            <div key={index} className="result-item">
              <div className="result-header">
                📄 {match.source_file} (Chunk {match.chunk_number})
              </div>
              <div className="result-content">
                {match.content}
              </div>
              <div className="similarity-score">
                Similarity: {similarity}%
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="container">
      <h1>🤖 AI Agent Chatbot - Vector Search</h1>

      <form className="search-form" onSubmit={handleSearch}>
        <input
          type="text"
          id="queryInput"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Enter your query here (e.g., 'camera configuration', 'frame rate', 'installation')"
          required
        />
        <button
          type="submit"
          id="searchButton"
          disabled={loading}
        >
          {loading ? "Searching..." : "Search"}
        </button>
      </form>

      {error && (
        <div className="error">
          {error}
        </div>
      )}

      {loading && (
        <div className="loading">
          🔍 Searching...
        </div>
      )}

      <div className="results">
        {displayResults()}
      </div>
    </div>
  );
}
