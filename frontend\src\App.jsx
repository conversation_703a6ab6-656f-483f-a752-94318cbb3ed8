import React, { useState, useEffect, useRef } from "react";
import './App.css';

export default function AIAgentChatbot() {
  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      content: "👋 Hi! I'm your AI assistant. Ask me anything about the technical documentation!",
      timestamp: new Date()
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, loading]);

  // Test backend connection on component mount
  useEffect(() => {
    const testBackendConnection = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/health');
        const data = await response.json();
        console.log('Backend status:', data);
      } catch (error) {
        setError('Cannot connect to backend server. Make sure it\'s running on port 5000.');
      }
    };

    testBackendConnection();
  }, []);

  async function handleSubmit(e) {
    e.preventDefault();
    if (!query.trim()) return;

    // Add user message to chat
    const userMessage = {
      id: Date.now(),
      type: "user",
      content: query,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);
    setError("");

    const currentQuery = query;
    setQuery(""); // Clear input immediately

    try {
      const response = await fetch("http://localhost:5000/api/chat/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ query: currentQuery }),
      });

      const data = await response.json();

      if (response.ok) {
        // Add bot response to chat
        const botMessage = {
          id: Date.now() + 1,
          type: "bot",
          content: data.answer,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botMessage]);
      } else {
        setError(data.error || "Error processing request");
      }
    } catch (error) {
      setError("Network error: " + error.message);
    }

    setLoading(false);
  }

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessages = () => {
    return messages.map((message) => (
      <div key={message.id} className={`message ${message.type}`}>
        <div className="message-content">
          <div className="message-text">
            {message.content}
          </div>
          <div className="message-time">
            {formatTime(message.timestamp)}
          </div>
        </div>
      </div>
    ));
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h1>🤖 AI Agent Chatbot</h1>
        <p className="subtitle">Technical Documentation Assistant</p>
      </div>

      <div className="chat-messages">
        {renderMessages()}

        {loading && (
          <div className="message bot typing">
            <div className="message-content">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div className="message-text">AI is thinking...</div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <form className="chat-input-form" onSubmit={handleSubmit}>
        <div className="input-container">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Type your question here..."
            disabled={loading}
            autoFocus
          />
          <button
            type="submit"
            disabled={loading || !query.trim()}
          >
            {loading ? "⏳" : "📤"}
          </button>
        </div>
      </form>
    </div>
  );
}
