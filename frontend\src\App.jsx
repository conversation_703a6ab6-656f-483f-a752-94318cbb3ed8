import React, { useState, useEffect } from "react";
import './App.css';

export default function AIAgentChatbot() {
  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [chatAnswer, setChatAnswer] = useState("");
  const [chatSources, setChatSources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [mode, setMode] = useState("chat"); // "chat" or "search"
  const [confidence, setConfidence] = useState(0);

  // Test backend connection on component mount
  useEffect(() => {
    const testBackendConnection = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/health');
        const data = await response.json();
        console.log('Backend status:', data);
      } catch (error) {
        setError('Cannot connect to backend server. Make sure it\'s running on port 5000.');
      }
    };

    testBackendConnection();
  }, []);

  async function handleSubmit(e) {
    e.preventDefault();
    setLoading(true);
    setSearchResults([]);
    setChatAnswer("");
    setChatSources([]);
    setError("");

    try {
      const endpoint = mode === "chat" ? "/api/chat/" : "/api/search/";
      const response = await fetch(`http://localhost:5000${endpoint}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ query }),
      });

      const data = await response.json();

      if (response.ok) {
        if (mode === "chat") {
          setChatAnswer(data.answer);
          setChatSources(data.sources || []);
          setConfidence(data.confidence || 0);
        } else {
          setSearchResults(data.matches || []);
        }
      } else {
        setError(data.error || "Error processing request");
      }
    } catch (error) {
      setError("Network error: " + error.message);
    }

    setLoading(false);
  }

  const displayChatAnswer = () => {
    if (!chatAnswer && !loading) {
      return <div className="no-results">Ask me anything about the technical documentation!</div>;
    }

    if (chatAnswer) {
      return (
        <div className="chat-response">
          <div className="answer-section">
            <h3>🤖 AI Assistant Answer:</h3>
            <div className="answer-content">
              {chatAnswer}
            </div>
            {confidence > 0 && (
              <div className="confidence-score">
                Confidence: {(confidence * 100).toFixed(1)}%
              </div>
            )}
          </div>

          {chatSources.length > 0 && (
            <div className="sources-section">
              <h4>📚 Sources ({chatSources.length}):</h4>
              {chatSources.map((source, index) => {
                const similarity = (source.similarity * 100).toFixed(1);
                return (
                  <div key={index} className="source-item">
                    <div className="source-header">
                      📄 {source.source_file} (Chunk {source.chunk_number}) - {similarity}% match
                    </div>
                    <div className="source-content">
                      {source.content.substring(0, 200)}...
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      );
    }
  };

  const displaySearchResults = () => {
    if (searchResults.length === 0) {
      return !loading && <div className="no-results">No search results found.</div>;
    }

    return (
      <div className="search-results">
        <h3>Found {searchResults.length} results for "{query}":</h3>
        {searchResults.map((match, index) => {
          const similarity = (match.similarity * 100).toFixed(1);
          return (
            <div key={index} className="result-item">
              <div className="result-header">
                📄 {match.source_file} (Chunk {match.chunk_number})
              </div>
              <div className="result-content">
                {match.content}
              </div>
              <div className="similarity-score">
                Similarity: {similarity}%
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="container">
      <h1>🤖 AI Agent Chatbot with RAG</h1>
      <p className="subtitle">Ask questions or search through technical documentation</p>

      {/* Mode Toggle */}
      <div className="mode-toggle">
        <button
          className={`mode-btn ${mode === "chat" ? "active" : ""}`}
          onClick={() => setMode("chat")}
        >
          💬 AI Chat
        </button>
        <button
          className={`mode-btn ${mode === "search" ? "active" : ""}`}
          onClick={() => setMode("search")}
        >
          🔍 Vector Search
        </button>
      </div>

      <form className="search-form" onSubmit={handleSubmit}>
        <input
          type="text"
          id="queryInput"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={
            mode === "chat"
              ? "Ask me anything about the documentation (e.g., 'How do I configure the camera?')"
              : "Search for specific content (e.g., 'camera configuration', 'frame rate')"
          }
          required
        />
        <button
          type="submit"
          id="submitButton"
          disabled={loading}
        >
          {loading ? (mode === "chat" ? "Thinking..." : "Searching...") : (mode === "chat" ? "Ask AI" : "Search")}
        </button>
      </form>

      {error && (
        <div className="error">
          {error}
        </div>
      )}

      {loading && (
        <div className="loading">
          {mode === "chat" ? "🧠 AI is thinking..." : "🔍 Searching..."}
        </div>
      )}

      <div className="results">
        {mode === "chat" ? displayChatAnswer() : displaySearchResults()}
      </div>
    </div>
  );
}
