�

    �Bh  �                   �  � d dl mZmZmZ d dlmZ d dlZd dlZd dl	m
Z
  ee�  �        Z ee�  �          e
d��  �        Z
dad� Zd� Zd	� Zdd�Ze�                    dd
g��  �        d� �   �         Ze�                    ddg��  �        d� �   �         Ze�                    ddg��  �        d� �   �         Zedk    rG ed�  �          e�   �         r% ed�  �         e�                    ddd��  �         dS  ed�  �         dS dS )�    )�Flask�request�jsonify)�CORSN)�OpenAIz�********************************************************************************************************************************************************************)�api_keyc                  �"  � 	 t          ddd��  �        5 } t          j        | �  �        addd�  �         n# 1 swxY w Y   t	          dt          t          �  �        � d��  �         dS # t          $ r}t	          d	|� ��  �         Y d}~d
S d}~ww xY w)z#Load embeddings from the JSON file.zJC:\Users\<USER>\Downloads\AI-Agent-Chatbot-main\embeddings_complete.json�rzutf-8)�encodingNu   ✅ Loaded z embeddingsTu   ❌ Error loading embeddings: F)�open�json�load�embeddings_data�print�len�	Exception)�f�es     �CC:\Users\<USER>\Downloads\AI-Agent-Chatbot-main\backend_server.py�load_embeddingsr      s�   � ��
�_�ad�ov�
w�
w�
w� 	+�{|�"�i��l�l�O�	+� 	+� 	+� 	+� 	+� 	+� 	+� 	+� 	+� 	+� 	+���� 	+� 	+� 	+� 	+�
�=�C��0�0�=�=�=�>�>�>��t��� � � �
�2�q�2�2�3�3�3��u�u�u�u�u��������s2   �A' �5�A' �9�A' �9�(A' �'
B�1B	�	Bc                 �h   � t           j        �                    | d��  �        }|j        d         j        S )zDGet vector embeddings for a given text using OpenAI's embedding API.ztext-embedding-ada-002)�input�modelr   )�
client_openai�
embeddings�create�data�	embedding)�text�responses     r   �
get_embeddingr!      s7   � ��'�.�.��&� /� � �H� �=���%�%�    c                 ��   � t          j        | �  �        } t          j        |�  �        }t          j        | |�  �        t           j        �                    | �  �        t           j        �                    |�  �        z  z  S )z0Calculate cosine similarity between two vectors.)�np�array�dot�linalg�norm)�a�bs     r   �cosine_similarityr+   '   sR   � �
�����A�
�����A�
�6�!�Q�<�<�2�9�>�>�!�,�,�r�y�~�~�a�/@�/@�@�A�Ar"   �   c                 ��  � t           sg S 	 t          | �  �        }g }t           D ]0}t          ||d         �  �        }|�                    ||d��  �         �1|�                    d� d��  �         g }|d|�         D ]<}|d         }|�                    |d         |d	         |d
         |d         d��  �         �=|S # t
          $ r}t
          d
|� ��  �         g cY d}~S d}~ww xY w)z2Search for similar chunks using cosine similarity.r   )�chunk�
similarityc                 �   � | d         S )Nr/   � )�xs    r   �<lambda>z'search_similar_chunks.<locals>.<lambda>@   s
   � ��,�� r"   T)�key�reverseNr.   �source_file�chunk_number�contentr/   )r6   r7   r8   r/   u   ❌ Error in search: )r   r!   r+   �append�sortr   r   )	�query�limit�query_embedding�similaritiesr.   r/   �results�itemr   s	            r   �search_similar_chunksrA   -   sP  � �� ��	��'��.�.�� ��$� 	� 	�E�*�?�E�+�<N�O�O�J�����(�!� !� 
� 
� 
� 
� 	���7�7���F�F�F� �� ��%��(� 	� 	�D���M�E��N�N�$�]�3� %�n� 5� ��+�"�<�0�	� � 
� 
� 
� 
� ���� � � �
�)�a�)�)�*�*�*��	�	�	�	�	�	��������s   �B+B7 �7
C�C�C�C�/api/search/�POST)�methodsc                  �  � 	 t          j        �   �         } | �                    dd�  �        �                    �   �         }|st	          ddi�  �        dfS t
          st	          ddi�  �        dfS t
          d|� d	��  �         t          |d
��  �        }t
          dt          |�  �        � d
��  �         t	          ||t          |�  �        d��  �        S # t          $ r/}t
          d|� ��  �         t	          ddi�  �        dfcY d}~S d}~ww xY w)zHandle search requests.r;   � �errorzQuery is requiredi�  zEmbeddings not loadedi�  u   🔍 Searching for: '�'r,   )r<   u
   ✅ Found z matches)r;   �matches�totalu   ❌ Error in search endpoint: zInternal server errorN)
r   �get_json�get�stripr   r   r   rA   r   r   )r   r;   rI   r   s       r   �searchrN   S   sV  � �@���!�!������"�%�%�+�+�-�-��� 	@��G�%8�9�:�:�C�?�?�� 	D��G�%<�=�>�>��C�C�
�.�e�.�.�.�/�/�/� (��Q�7�7�7��
�1�3�w�<�<�1�1�1�2�2�2������\�\�
� 
� � � 	�� � @� @� @�
�2�q�2�2�3�3�3���!8�9�:�:�C�?�?�?�?�?�?�?�����@���s+   �AC �C �,A#C �
D	�$D�>D	�D	�/api/health�GETc                  �l   � t          dt          dut          rt          t          �  �        ndd��  �        S )zHealth check endpoint.�healthyNr   )�status�embeddings_loaded�total_chunks)r   r   r   r1   r"   r   �healthrV   q   s@   � � ��,�D�8�0?�F��O�,�,�,�Q�� � � � r"   �/c                  �,   � t          dddd�d��  �        S )zHome endpoint.zAI Agent Chatbot Backend APIz!POST - Search for similar contentzGET - Health check)rB   rO   )�message�	endpoints)r   r1   r"   r   �homer[   z   s3   � � �1�?�/�
� 
�� � � � r"   �__main__u)   🚀 Starting AI Agent Chatbot Backend...u   ✅ Backend ready!z0.0.0.0i�  T)�host�port�debugu'   ❌ Failed to load embeddings. Exiting.)r,   )�flaskr   r   r   �
flask_corsr   r
   �numpyr$   �openair   �__name__�appr   r   r   r!   r+   rA   �routerN   rV   r[   r   �runr1   r"   r   �<module>rh      s�  �� )� )� )� )� )� )� )� )� )� )� � � � � � � ���� � � � � � � � � � � �e�H�o�o�� ��S�	�	�	� �� 
s�� � �
�
 ��
� 
� 
�&� &� &�B� B� B�$� $� $� $�L ���>�F�8��,�,�@� @� -�,�@�: ���=�5�'��*�*�� � +�*�� ���3���� � �� � !� �� �z�� 9�	�E�
5�6�6�6� ���� 9�
��"�#�#�#����Y�T���6�6�6�6�6�
��7�8�8�8�8�8�9� 9r"   