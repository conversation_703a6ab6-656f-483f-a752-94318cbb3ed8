from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
import json
import weaviate
from openai import OpenAI

# === Configuration ===
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

# Initialize OpenAI client
client_openai = OpenAI(api_key=OPENAI_API_KEY)

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model=EMBEDDING_MODEL
    )
    return response.data[0].embedding

def generate_answer(query, context_chunks):
    """Generate an answer using GPT with retrieved chunks."""
    context_text = "\n".join(context_chunks)

    prompt = f"""You are a helpful AI assistant specializing in technical documentation and camera systems. Use the following context to answer the user's question accurately and comprehensively.

Context:
{context_text}

Question: {query}

Instructions:
- Provide a clear, detailed answer based on the context
- If the context doesn't contain enough information, say so
- Include specific technical details when available
- Be concise but thorough

Answer:"""

    try:
        completion = client_openai.chat.completions.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=500
        )
        return completion.choices[0].message.content.strip()
    except Exception as e:
        return f"❌ OpenAI error: {e}"

def search_similar_chunks_weaviate(query, limit=5):
    """Search for similar chunks using Weaviate vector database."""
    try:
        # Get embedding for the query
        query_embedding = get_embedding(query)
        
        # Connect to Weaviate - use the correct connection parameters
        with weaviate.Client(
            url="http://localhost:8080",  # Use the exact URL from docker-compose
        ) as client:
            # Use the correct class name that exists in your database
            # This might be different from WEAVIATE_CLASS_NAME if you imported data differently
            collection_name = WEAVIATE_CLASS_NAME
            
            # Check if the class exists
            schema = client.schema.get()
            available_classes = [cls['class'] for cls in schema['classes']] if 'classes' in schema else []
            
            if collection_name not in available_classes and available_classes:
                # If the specified class doesn't exist but others do, use the first available
                collection_name = available_classes[0]
                print(f"Using existing collection: {collection_name}")
            
            # Perform vector search using GraphQL
            result = client.query.get(
                collection_name, 
                ["source_file", "chunk_number", "content"]
            ).with_near_vector({
                "vector": query_embedding,
                "certainty": 0.7
            }).with_limit(limit).do()
            
            # Process results
            results = []
            if result and "data" in result and "Get" in result["data"]:
                objects = result["data"]["Get"].get(collection_name, [])
                for obj in objects:
                    results.append({
                        "source_file": obj.get("source_file", "Unknown"),
                        "chunk_number": obj.get("chunk_number", 0),
                        "content": obj.get("content", ""),
                        "similarity": obj.get("_additional", {}).get("certainty", 0)
                    })
            
            return results
            
    except Exception as e:
        print(f"❌ Error in Weaviate search: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def retrieve_and_generate_answer(query_text, top_k=5):
    """Retrieve top K relevant chunks and generate an AI answer."""
    try:
        # Get similar chunks from Weaviate
        matches = search_similar_chunks_weaviate(query_text, limit=top_k)

        if not matches:
            return {
                "answer": "⚠️ I don't have information about that topic in my knowledge base, or the Weaviate database is not available. Please ensure Weaviate is running with Docker."
            }

        # Extract content for context
        context_chunks = [match["content"] for match in matches]

        # Generate answer using GPT
        answer = generate_answer(query_text, context_chunks)

        return {
            "answer": answer,
            "matches": matches
        }
    except Exception as e:
        print(f"❌ Error in retrieve_and_generate_answer: {e}")
        return {
            "answer": f"❌ Sorry, I encountered an error while processing your question: {e}"
        }

# === Django API Views ===

@api_view(['POST'])
def chat(request):
    """Handle chat requests - returns AI-generated answers."""
    try:
        data = request.data
        query = data.get('query', '').strip()

        if not query:
            return Response({"error": "Query is required"}, status=status.HTTP_400_BAD_REQUEST)

        print(f"💬 Chat query: '{query}'")

        # Retrieve and generate answer using Weaviate
        result = retrieve_and_generate_answer(query, top_k=5)

        print(f"✅ Generated answer")

        return Response({
            "query": query,
            "answer": result["answer"],
            "matches": result.get("matches", [])
        })

    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return Response({"error": f"Internal server error: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def health(request):
    """Health check endpoint."""
    weaviate_status = "unavailable"
    collections = []
    counts = {}
    
    try:
        client = weaviate.Client(url="http://localhost:8080")
        
        # Get schema to see what collections exist
        schema = client.schema.get()
        if 'classes' in schema:
            collections = [cls['class'] for cls in schema['classes']]
            
            # Get count for each collection
            for collection in collections:
                result = client.query.aggregate(collection).with_meta_count().do()
                if result and "data" in result and "Aggregate" in result["data"]:
                    count = result["data"]["Aggregate"][collection][0]["meta"]["count"]
                    counts[collection] = count
            
            weaviate_status = "connected"
        
        client.close()
        
    except Exception as e:
        weaviate_status = f"error: {str(e)}"
        
    return Response({
        "status": "healthy",
        "weaviate_status": weaviate_status,
        "collections": collections,
        "counts": counts
    })

@api_view(['GET'])
def home(request):
    """Home endpoint."""
    return Response({
        "message": "AI Agent Chatbot Backend API with RAG (Django)",
        "version": "2.0",
        "framework": "Django + Django REST Framework",
        "endpoints": {
            "/api/chat/": "POST - AI-powered chat with context (RAG)",
            "/api/health/": "GET - Health check"
        },
        "features": [
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Weaviate vector database integration",
            "Technical documentation expertise"
        ]
    })
