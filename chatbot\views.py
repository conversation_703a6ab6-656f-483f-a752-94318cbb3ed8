from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
import json
import weaviate
from openai import OpenAI
import weaviate
from weaviate.util import generate_uuid5

# === Configuration ===
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

# Initialize OpenAI client
client_openai = OpenAI(api_key=OPENAI_API_KEY)

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model=EMBEDDING_MODEL
    )
    return response.data[0].embedding

def generate_answer(query, context_chunks):
    """Generate an answer using GPT with retrieved chunks."""
    context_text = "\n".join(context_chunks)

    prompt = f"""You are a helpful AI assistant specializing in technical documentation and camera systems. Use the following context to answer the user's question accurately and comprehensively.

Context:
{context_text}

Question: {query}

Instructions:
- Provide a clear, detailed answer based on the context
- If the context doesn't contain enough information, say so
- Include specific technical details when available
- Be concise but thorough

Answer:"""

    try:
        completion = client_openai.chat.completions.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=500
        )
        return completion.choices[0].message.content.strip()
    except Exception as e:
        return f"❌ OpenAI error: {e}"

def search_similar_chunks_weaviate(query, limit=5):
    """Search for similar chunks using Weaviate vector database."""
    try:
        # Get embedding for the query
        query_embedding = get_embedding(query)

        # Connect to Weaviate using v4 client
        with weaviate.connect_to_local() as client:
            # Check if collection exists
            if not client.collections.exists(WEAVIATE_CLASS_NAME):
                print(f"❌ Collection '{WEAVIATE_CLASS_NAME}' not found in Weaviate")
                return []

            # Get the collection
            collection = client.collections.get(WEAVIATE_CLASS_NAME)

            # Perform vector search
            response = collection.query.near_vector(
                near_vector=query_embedding,
                limit=limit,
                return_metadata=weaviate.classes.query.MetadataQuery(distance=True)
            )

            # Process results
            results = []
            for obj in response.objects:
                # Calculate similarity from distance (distance = 1 - similarity)
                similarity = 1 - obj.metadata.distance if obj.metadata.distance else 0

                results.append({
                    "source_file": obj.properties.get("source_file", "Unknown"),
                    "chunk_number": obj.properties.get("chunk_number", 0),
                    "content": obj.properties.get("content", ""),
                    "similarity": similarity
                })

            return results

    except Exception as e:
        print(f"❌ Error in Weaviate search: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def retrieve_and_generate_answer(query_text, top_k=5):
    """Retrieve top K relevant chunks and generate an AI answer."""
    try:
        # Get similar chunks from Weaviate
        matches = search_similar_chunks_weaviate(query_text, limit=top_k)

        if not matches:
            return {
                "answer": "⚠️ I don't have information about that topic in my knowledge base, or the Weaviate database is not available. Please ensure Weaviate is running with Docker."
            }

        # Extract content for context
        context_chunks = [match["content"] for match in matches]

        # Generate answer using GPT
        answer = generate_answer(query_text, context_chunks)

        return {
            "answer": answer,
            "matches": matches
        }
    except Exception as e:
        print(f"❌ Error in retrieve_and_generate_answer: {e}")
        return {
            "answer": f"❌ Sorry, I encountered an error while processing your question: {e}"
        }

# === Django API Views ===

@api_view(['POST'])
def chat(request):
    """Handle chat requests - returns AI-generated answers."""
    try:
        data = request.data
        query = data.get('query', '').strip()

        if not query:
            return Response({"error": "Query is required"}, status=status.HTTP_400_BAD_REQUEST)

        print(f"💬 Chat query: '{query}'")

        # Retrieve and generate answer using Weaviate
        result = retrieve_and_generate_answer(query, top_k=5)

        print(f"✅ Generated answer")

        return Response({
            "query": query,
            "answer": result["answer"],
            "matches": result.get("matches", [])
        })

    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return Response({"error": f"Internal server error: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def health(request):
    """Health check endpoint."""
    weaviate_status = "unavailable"
    collections = []
    counts = {}

    try:
        with weaviate.connect_to_local() as client:
            # Check if our target collection exists
            if client.collections.exists(WEAVIATE_CLASS_NAME):
                collection = client.collections.get(WEAVIATE_CLASS_NAME)

                # Get collection count
                result = collection.aggregate.over_all(total_count=True)
                total_objects = result.total_count

                collections = [WEAVIATE_CLASS_NAME]
                counts[WEAVIATE_CLASS_NAME] = total_objects
                weaviate_status = "connected"

                print(f"✅ Weaviate health check: {total_objects} objects in {WEAVIATE_CLASS_NAME}")
            else:
                weaviate_status = f"Collection '{WEAVIATE_CLASS_NAME}' not found"
                print(f"❌ Collection '{WEAVIATE_CLASS_NAME}' not found in Weaviate")

    except Exception as e:
        weaviate_status = f"error: {str(e)}"
        print(f"❌ Weaviate health check failed: {e}")

    return Response({
        "status": "healthy",
        "weaviate_status": weaviate_status,
        "collections": collections,
        "counts": counts,
        "target_collection": WEAVIATE_CLASS_NAME
    })

@api_view(['GET'])
def home(request):
    """Home endpoint."""
    return Response({
        "message": "AI Agent Chatbot Backend API with RAG (Django)",
        "version": "2.0",
        "framework": "Django + Django REST Framework",
        "endpoints": {
            "/api/chat/": "POST - AI-powered chat with context (RAG)",
            "/api/health/": "GET - Health check"
        },
        "features": [
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Weaviate vector database integration",
            "Technical documentation expertise"
        ]
    })
