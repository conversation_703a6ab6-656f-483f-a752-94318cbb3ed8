from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import numpy as np
from openai import OpenAI

# === Configuration ===
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMBEDDING_MODEL = "text-embedding-ada-002"
GPT_MODEL = "gpt-4o-mini"

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize OpenAI client
client_openai = OpenAI(api_key=OPENAI_API_KEY)

# Global variable to store embeddings
embeddings_data = None

def load_embeddings():
    """Load embeddings from the JSON file."""
    global embeddings_data
    try:
        with open(r"C:\Users\<USER>\Downloads\AI-Agent-Chatbot-main\embeddings_complete.json", "r", encoding="utf-8") as f:
            embeddings_data = json.load(f)
        print(f"✅ Loaded {len(embeddings_data)} embeddings")
        return True
    except Exception as e:
        print(f"❌ Error loading embeddings: {e}")
        return False

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model=EMBEDDING_MODEL
    )
    return response.data[0].embedding

def generate_answer(query, context_chunks):
    """Generate an answer using GPT with retrieved chunks."""
    context_text = "\n".join(context_chunks)

    prompt = f"""You are a helpful AI assistant specializing in technical documentation and camera systems. Use the following context to answer the user's question accurately and comprehensively.

Context:
{context_text}

Question: {query}

Instructions:
- Provide a clear, detailed answer based on the context
- If the context doesn't contain enough information, say so
- Include specific technical details when available
- Be concise but thorough

Answer:"""

    try:
        completion = client_openai.chat.completions.create(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=500
        )
        return completion.choices[0].message.content.strip()
    except Exception as e:
        return f"❌ OpenAI error: {e}"

def cosine_similarity(a, b):
    """Calculate cosine similarity between two vectors."""
    a = np.array(a)
    b = np.array(b)
    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

def search_similar_chunks(query, limit=5):
    """Search for similar chunks using cosine similarity."""
    if not embeddings_data:
        return []

    try:
        # Get embedding for the query
        query_embedding = get_embedding(query)

        # Calculate similarities
        similarities = []
        for chunk in embeddings_data:
            similarity = cosine_similarity(query_embedding, chunk["embedding"])
            similarities.append({
                "chunk": chunk,
                "similarity": similarity
            })

        # Sort by similarity (highest first)
        similarities.sort(key=lambda x: x["similarity"], reverse=True)

        # Return top results
        results = []
        for item in similarities[:limit]:
            chunk = item["chunk"]
            results.append({
                "source_file": chunk["source_file"],
                "chunk_number": chunk["chunk_number"],
                "content": chunk["content"],
                "similarity": item["similarity"]
            })

        return results

    except Exception as e:
        print(f"❌ Error in search: {e}")
        return []

def retrieve_and_generate_answer(query_text, top_k=5):
    """Retrieve top K relevant chunks and generate an AI answer."""
    try:
        # Get similar chunks
        matches = search_similar_chunks(query_text, limit=top_k)

        if not matches:
            return {
                "answer": "⚠️ No relevant content found in the knowledge base.",
                "sources": [],
                "confidence": 0
            }

        # Extract content for context
        context_chunks = [match["content"] for match in matches]

        # Generate answer using GPT
        answer = generate_answer(query_text, context_chunks)

        # Calculate average confidence (similarity score)
        avg_confidence = sum(match["similarity"] for match in matches) / len(matches)

        return {
            "answer": answer,
            "sources": matches,
            "confidence": avg_confidence,
            "total_sources": len(matches)
        }

    except Exception as e:
        return {
            "answer": f"❌ Error generating answer: {e}",
            "sources": [],
            "confidence": 0
        }

@app.route('/api/search/', methods=['POST'])
def search():
    """Handle search requests - returns raw search results."""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({"error": "Query is required"}), 400

        if not embeddings_data:
            return jsonify({"error": "Embeddings not loaded"}), 500

        print(f"🔍 Searching for: '{query}'")

        # Search for similar chunks
        matches = search_similar_chunks(query, limit=5)

        print(f"✅ Found {len(matches)} matches")

        return jsonify({
            "query": query,
            "matches": matches,
            "total": len(matches)
        })

    except Exception as e:
        print(f"❌ Error in search endpoint: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/chat/', methods=['POST'])
def chat():
    """Handle chat requests - returns AI-generated answers with sources."""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()

        if not query:
            return jsonify({"error": "Query is required"}), 400

        if not embeddings_data:
            return jsonify({"error": "Embeddings not loaded"}), 500

        print(f"💬 Chat query: '{query}'")

        # Retrieve and generate answer
        result = retrieve_and_generate_answer(query, top_k=5)

        print(f"✅ Generated answer with {result['total_sources']} sources")

        return jsonify({
            "query": query,
            "answer": result["answer"],
            "sources": result["sources"],
            "confidence": result["confidence"],
            "total_sources": result["total_sources"]
        })

    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "embeddings_loaded": embeddings_data is not None,
        "total_chunks": len(embeddings_data) if embeddings_data else 0
    })

@app.route('/', methods=['GET'])
def home():
    """Home endpoint."""
    return jsonify({
        "message": "AI Agent Chatbot Backend API with RAG",
        "version": "2.0",
        "endpoints": {
            "/api/search/": "POST - Search for similar content (raw results)",
            "/api/chat/": "POST - AI-powered chat with context (RAG)",
            "/api/health": "GET - Health check"
        },
        "features": [
            "Vector similarity search",
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Source attribution",
            "Confidence scoring"
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting AI Agent Chatbot Backend...")
    
    # Load embeddings on startup
    if load_embeddings():
        print("✅ Backend ready!")
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        print("❌ Failed to load embeddings. Exiting.")
